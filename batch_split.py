import os
import argparse
import logging
from tqdm import tqdm
# from modelscope.pipelines import pipeline
# from modelscope.utils.constant import Tasks
import json
import time
from datetime import datetime, timedelta
# from concurrent.futures import ThreadPoolExecutor
# from tqdm.contrib.concurrent import thread_map, process_map
from pgl_sum.video_summarization_pipeline import VideoSummarizationPipeline, TooShortVideoError, build_ffmpeg_cmd, print_gpu_memory
import subprocess

timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M")

# Supported video extensions
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

def pause_during_night(start_hour=13, end_hour=1):
    now = datetime.now()
    # 判断是否处于 21:00–次日09:00
    if now.hour >= start_hour or now.hour < end_hour:
        # 计算下一个唤醒时间
        if now.hour >= start_hour:
            wake = datetime(now.year, now.month, now.day, end_hour) + timedelta(days=1)
        else:
            wake = datetime(now.year, now.month, now.day, end_hour)
        delay = (wake - now).total_seconds()
        print(f"当前时间 {now.time()} 在禁用时段，程序将暂停 {delay/3600:.2f} 小时")
        time.sleep(delay)  # 阻塞当前线程至区间结束 :contentReference[oaicite:6]{index=6}
    else:
        print(f"当前时间 {now.time()} 在允许时段，继续执行")


def process_videos(input_base: str, output_base: str):
    summarization_pipeline = VideoSummarizationPipeline(model='/home/<USER>/.cache/modelscope/hub/models/iic/cv_googlenet_pgl-video-summarization')
    # Collect all video files first
    video_paths = []
    for root, _, files in os.walk(input_base):
        for fname in files:
            if fname.lower().endswith(VIDEO_EXTENSIONS):
                video_paths.append(os.path.join(root, fname))

    # Ensure base output directory exists
    os.makedirs(output_base, exist_ok=True)

    # Path for record of processed files
    processed_file = os.path.join(output_base, 'processed.log')
    # Load already processed files
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # Filter out already processed videos
    pending_paths = [p for p in video_paths if p not in processed_set]

    # Setup separate loggers for success and error
    success_log = os.path.join(output_base, 'success.log')
    error_log = os.path.join(output_base, 'error.log')

    # Error logger
    error_logger = logging.getLogger('whisperx_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    # Success logger
    success_logger = logging.getLogger('whisperx_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    for input_path in tqdm(pending_paths[2:], desc="Processing videos", unit="file"):
    # def run_inference(input_path):
        # pause_during_night()
        # Determine output directory based on relative path and filename
        # if True:
        # print(f"now processing: {input_path}")

        root = os.path.dirname(input_path)
        fname = os.path.basename(input_path)
        rel_dir = os.path.relpath(root, input_base)
        output_dir = os.path.join(output_base, rel_dir)
        os.makedirs(output_dir, exist_ok=True)
        base_name, _ = os.path.splitext(fname)
        output_json = os.path.join(output_dir, base_name+".json")

        try:
            result = summarization_pipeline(input_path)
            with open(output_json, "w") as f:
                json.dump(result,f)
            success_logger.info(f"Successfully processed: {input_path}")
            with open(processed_file, 'a') as pf:
                pf.write(f"{input_path}\n")
        except TooShortVideoError:
            success_logger.info(f"Video Too Short: {input_path}")
            with open(processed_file, 'a') as pf:
                pf.write(f"{input_path}\n")

        except Exception as e:
            error_logger.error(
                f"Failed processing '{input_path}'. Return: {e}, try convert\n"
            )
            cvt_path, cmd = build_ffmpeg_cmd(input_path)
            try:
                ret = subprocess.run(cmd[0], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                assert ret.returncode == 0, ret.stderr
                result = summarization_pipeline(cvt_path)
                with open(output_json, "w") as f:
                    json.dump(result,f)
                success_logger.info(f"Converted and processed: {input_path}")
                with open(processed_file, 'a') as pf:
                    pf.write(f"{input_path}\n")
                    pf.write(f"{cvt_path}\n")
            except Exception as e:
                error_logger.error(
                    f"Failed convert '{input_path}'. Return: {e}\n"
                )
                subprocess.run(cmd[1], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

    # pending_paths = pending_paths[:4]
    # results = thread_map(run_inference, pending_paths, max_workers=1, desc="Threads")

    # for record in results:
    #     out = record["output"]
    #     print(f"Output={out}")

def main():
    parser = argparse.ArgumentParser(
        description='Batch process videos with whisperx across a directory tree.'
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )

    args = parser.parse_args()

    log_file = os.path.join(args.output_base, 'log.txt') 
    log = open(log_file, "w")
    os.dup2(log.fileno(), 1)
    os.dup2(log.fileno(), 2)

    process_videos(args.input_base, args.output_base)


if __name__ == '__main__':
    main()
