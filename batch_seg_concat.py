import json
import subprocess
import os
import tempfile
from contextlib import contextmanager
import logging
from tqdm import tqdm
import argparse
from tqdm.contrib.concurrent import thread_map
import time
from datetime import datetime, timedelta

from pgl_sum.video_summarization_pipeline import build_ffmpeg_cmd

cur_time = datetime.now().strftime("%Y-%m-%d-%H-%M")

VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)


@contextmanager
def temp_dir_manager():
    """临时目录上下文管理器"""
    with tempfile.TemporaryDirectory() as tempdir:
        try:
            yield tempdir
        finally:
            pass  # 自动清理临时目录

def get_video_size(input_file):
    """获取视频分辨率"""
    cmd = [
        'ffprobe', '-v', 'error',
        '-select_streams', 'v:0',
        '-show_entries', 'stream=width,height',
        '-of', 'csv=p=0',
        input_file
    ]
    try:
        w, h = subprocess.check_output(cmd).decode().strip().split(',')
        return int(w), int(h)
    except Exception as e:
        raise Exception(f"获取分辨率失败: {e}")

def get_audio(input_file):
    cmd = [
        'ffprobe',
        '-v', 'error',
        '-show_streams',
        '-print_format', 'json',
        input_file
    ]
    try:
        output = subprocess.check_output(cmd, stderr=subprocess.STDOUT)
        parsed = json.loads(output)
        streams = parsed.get('streams', [])
        # 检查是否存在音频流
        return any(stream.get('codec_type') == 'audio' for stream in streams)
    except subprocess.CalledProcessError as e:
        print(f"ffprobe 命令执行失败: {e.output.decode()}")
        return False
    except json.JSONDecodeError as e:
        print(f"解析 ffprobe 输出时出错: {e}")
        return False

def process_segment_group(tempdir, input_file, segments, position):
    """处理单个宫格位置的视频拼接"""
    concat_list = os.path.join(tempdir, f'concat_{position}.txt')
    output_file = os.path.join(tempdir, f'position_{position}.mp4')

    # 生成切割命令列表
    with open(concat_list, 'w') as f:
        for idx, seg in enumerate(segments):
            clip_path = os.path.join(tempdir, f'pos{position}_clip{idx}.mp4')
            # 'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
            # '-ss', seg['timestamps'][0], '-to', seg['timestamps'][1],
            # '-i', input_file, '-c', 'copy', clip_path
            cmd = [
                'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
                '-ss', seg['timestamps'][0], '-to', seg['timestamps'][1],
                '-i', input_file, '-c:v', 'libx264', '-preset', 'medium', '-crf', '23',
                '-c:a', 'aac', '-b:a', '192k', '-af', 'apad', '-shortest', clip_path
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            assert result.returncode == 0, result.stderr
            f.write(f"file '{clip_path}'\n")

    # 合并片段
    cmd = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-f', 'concat', '-safe', '0', '-i', concat_list,
        '-c', 'copy', output_file
    ]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    assert result.returncode == 0, result.stderr
    return output_file

def get_video_duration(input_file):
    """获取视频时长(秒)"""
    cmd = [
        'ffprobe', '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'csv=p=0',
        input_file
    ]
    try:
        return float(subprocess.check_output(cmd).decode().strip())
    except Exception as e:
        raise Exception(f"获取时长失败: {e}")

def trim_video(tempdir, input_file, duration, suffix):
    """截断视频到指定时长"""
    output = os.path.join(tempdir, f'trimmed_{suffix}.mp4')
    cmd = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-i', input_file, '-t', str(duration),
        '-c', 'copy', output
    ]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    assert result.returncode == 0, result.stderr
    return output

def create_grid(tempdir, videos, width, height, output_file):
    """创建四宫格视频"""
    cmd = [
        'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
        '-i', videos[0], '-i', videos[1],
        '-i', videos[2], '-i', videos[3],
        '-filter_complex',
        f'''
        [0:v]scale={width//2}:{height//2}[v0];
        [1:v]scale={width//2}:{height//2}[v1];
        [2:v]scale={width//2}:{height//2}[v2];
        [3:v]scale={width//2}:{height//2}[v3];
        [v0][v1][v2][v3]xstack=inputs=4:layout=0_0|w0_0|0_h0|w0_h0[outv];
        [0:a]aresample=async=1[outa]
        ''',
        '-map', '[outv]',
        '-map', '[outa]',
        '-c:v', 'libx264', '-preset', 'fast', '-crf', '23',
        '-c:a', 'aac', '-b:a', '128k',
        output_file
    ]

    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    assert result.returncode == 0, result.stderr

def process_video_clips(segments, input_file, output_file):

    if os.path.exists(output_file):
        os.remove(output_file)
    with temp_dir_manager() as tempdir:

        if len(segments) >= 4:
            # 跳过片头，取整至4的倍数
            # segments_end = ((len(segments)-2) // 4 * 4)+2
            # segments = segments[2:segments_end]
            segments_end = len(segments) // 4 * 4
            segments = segments[:segments_end]
        else:
            raise ValueError("视频片段数量不足4个")

        # 获取原始视频分辨率
        orig_width, orig_height = get_video_size(input_file)

        # 按宫格位置分组 (0:左上, 1:右上, 2:左下, 3:右下)
        position_groups = [[] for _ in range(4)]
        for idx, seg in enumerate(segments):
            position_groups[idx % 4].append(seg)

        # 处理每个宫格位置的视频
        position_videos = []
        for pos in range(4):
            if not position_groups[pos]:
                raise ValueError(f"错误：宫格位置 {pos} 没有视频片段")
            merged = process_segment_group(tempdir, input_file, position_groups[pos], pos)
            position_videos.append(merged)

        # 获取最短时长
        durations = [get_video_duration(v) for v in position_videos]
        min_duration = min(durations)

        # 截断所有视频到相同时长
        trimmed_videos = []
        for idx, video in enumerate(position_videos):
            trimmed = trim_video(tempdir, video, min_duration, idx)
            trimmed_videos.append(trimmed)

        # 生成最终四宫格
        create_grid(tempdir, trimmed_videos, orig_width, orig_height, output_file)


def process_videos(input_base: str, json_base: str):

    # Collect all video files first
    video_paths = []
    for root, _, files in os.walk(input_base):
        for fname in files:
            if fname.lower().endswith(VIDEO_EXTENSIONS) and (not "thumbnails" in root):
                video_paths.append(os.path.join(root, fname))

    # Path for record of processed files
    processed_file = 'processed_video.log'
    # Load already processed files
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # Filter out already processed videos
    pending_paths = [p for p in video_paths if p not in processed_set]

    # Setup separate loggers for success and error
    log_dir = os.path.join(os.getcwd(), 'video_logs')
    success_log = os.path.join(log_dir, cur_time + '-success.log')
    error_log = os.path.join(log_dir, cur_time + '-error.log')

    # Error logger
    error_logger = logging.getLogger('whisperx_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    # Success logger
    success_logger = logging.getLogger('whisperx_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    # Process with progress bar
    def run_concat(input_path):
    # for input_path in tqdm(pending_paths, desc="Processing videos", unit="file"):
        # Determine output directory based on relative path and filename
        root = os.path.dirname(input_path)
        fname = os.path.basename(input_path)
        rel_dir = os.path.relpath(root, input_base)
        base_name, _ = os.path.splitext(fname)

        json_dir = os.path.join(json_base, rel_dir)
        json_file = os.path.join(json_dir, base_name+".json")
        if not os.path.exists(json_file):
            error_logger.error(
                f"JSON file not found for '{input_path}'. Expected: {json_file}\n"
            )
            # continue
            return

        output_dir = os.path.join(root, "thumbnails")
        output_path = os.path.join(output_dir, base_name+".mp4")
        os.makedirs(output_dir, exist_ok=True)

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(input_path)
            process_video_clips(data.get('output', []), input_path, output_path)
            # Log success and mark as processe
            success_logger.info(f"Successfully processed: {input_path}")
            with open(processed_file, 'a') as pf:
                pf.write(f"{input_path}\n")
        # except Exception as e:
        #     error_logger.error(
        #         f"Failed processing '{input_path}'. Return: {e}\n"
        #     )
        #     return

            # Log success and mark as processed
        except ValueError as e:
            error_logger.error(
                f"Failed processing '{input_path}'. Return: {e}\n"
            )
        except Exception:
            cvt_path, cmd = build_ffmpeg_cmd(input_path)
            try:
                ret = subprocess.run(cmd[0], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                assert ret.returncode == 0, ret.stderr
                process_video_clips(data.get('output', []), cvt_path, output_path)
                # Log success and mark as processe
                success_logger.info(f"Successfully converted and processed: {input_path}")
                with open(processed_file, 'a') as pf:
                    pf.write(f"{input_path}\n")
                    pf.write(f"{cvt_path}\n")
            except Exception:
                subprocess.run(cmd[1], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)


    results = thread_map(run_concat, pending_paths, max_workers=32, desc="Threads")

    # for record in results:
    #     # inp = record["input"]
    #     out = record["output"]
    #     print(f"Output={out}")

def main():
    parser = argparse.ArgumentParser(
        description='Batch process videos with whisperx across a directory tree.'
    )
    
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'json_base'
    )

    args = parser.parse_args()

    # log_dir = os.path.join(os.getcwd(), 'video_logs')

    # log_file = os.path.join(log_dir, 'dump.txt') 
    # log = open(log_file, "w")
    # os.dup2(log.fileno(), 1)
    # os.dup2(log.fileno(), 2)
    
    process_videos(args.input_base, args.json_base)


if __name__ == '__main__':
    main()