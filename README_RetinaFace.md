# 增强版RetinaFace批量视频人脸检测程序

## 概述

这是一个增强版的RetinaFace批量视频人脸检测程序，在原有功能基础上集成了**Hopenet头部姿态估计**和**人脸偏离度计算**功能。程序能够同时进行人脸检测、面部特征点可视化、头部朝向分析和偏离度计算，并提供丰富的可视化效果。

## 🚀 新增功能

### ✅ 任务1：人脸朝向检测和坐标轴可视化
- **Hopenet集成**: 使用深度学习模型进行精确的头部姿态估计
- **3D坐标轴可视化**: 在检测到的人脸上绘制3D坐标轴
  - 红色线条: X轴 (左右方向)
  - 绿色线条: Y轴 (上下方向)
  - 蓝色线条: Z轴 (前后方向)
- **姿态角度显示**: 显示Yaw（左右转）、Pitch（上下点头）、Roll（左右倾斜）角度

### ✅ 任务2：人脸偏离度计算和显示
- **偏离度计算**: 基于头部姿态角度计算人脸与屏幕垂直方向的偏离度
- **醒目显示**: 在图像左上角以大号黄色字体显示偏离度数值
- **实时反馈**: 数值越小表示越正对屏幕
- **多人脸处理**: 显示最小偏离度（最正对屏幕的人脸）

## 🎯 核心特性

### 原有功能（完全保持）
- ✅ **RetinaFace人脸检测**: 使用ModelScope的RetinaFace模型，提供高精度人脸检测
- ✅ **面部特征点可视化**: 完整的5个关键点可视化功能
- ✅ **批量处理**: 支持递归遍历目录树，批量处理多个视频文件
- ✅ **智能采样**: 每3秒抽取一帧进行检测，提高处理效率
- ✅ **质量过滤**: 自动过滤低亮度、低对比度的帧
- ✅ **尺寸过滤**: 过滤过小的人脸检测框，确保检测质量
- ✅ **多帧拼接**: 生成包含多个连续帧的拼接图像
- ✅ **进度跟踪**: 显示处理进度条和详细日志
- ✅ **断点续传**: 支持从上次中断的地方继续处理

### 新增功能
- ✅ **Hopenet头部姿态估计**: 精确计算Yaw、Pitch、Roll角度
- ✅ **3D坐标轴可视化**: 直观显示头部朝向的3D坐标轴
- ✅ **人脸偏离度计算**: 量化人脸与屏幕垂直方向的偏离程度
- ✅ **增强可视化**: 丰富的颜色编码和信息显示

## 文件说明

### 核心文件
- `batch_retinaface.py` - 主程序文件，包含完整的RetinaFace批量处理功能

### 原始文件（保持不变）
- `batch_yolo11.py` - 原始的YOLO11实现（未修改）

## 使用方法

### 基本用法

```bash
python3 batch_retinaface.py <输入目录> <输出目录>
```

### 示例

```bash
# 处理test目录下的所有视频，输出到output目录
python3 batch_retinaface.py ../test ../output

# 处理videos目录下的所有视频，输出到results目录
python3 batch_retinaface.py /path/to/videos /path/to/results
```

## 输出文件

程序会在输出目录中生成以下文件：

1. **检测结果图像**:
   - `<视频名>.jpg` - 首次检测到人脸的单帧图像（带检测框和关键点）
   - `<视频名>.jpg.jpg` - 多帧拼接图像

2. **日志文件**:
   - `success.log` - 成功处理的视频列表
   - `error.log` - 处理失败的视频和错误信息
   - `processed.log` - 已处理的视频列表（用于断点续传）

## 📋 依赖要求

### 核心依赖
```bash
pip install opencv-python
pip install numpy
pip install tqdm
pip install modelscope
pip install torch torchvision
pip install pillow
```

### Hopenet模型
- 需要Hopenet权重文件：`hopenet_robust_alpha1.pkl`
- 程序会自动查找权重文件路径

### 可选依赖（GPU加速）
```bash
pip install cvcuda
```

## 技术特点

### RetinaFace vs YOLO11

| 特性 | RetinaFace | YOLO11 |
|------|------------|--------|
| 专业性 | 专门的人脸检测模型 | 通用目标检测模型 |
| 精度 | 更高的人脸检测精度 | 较好的通用检测能力 |
| 关键点 | 支持人脸关键点检测 | 仅边界框检测 |
| 模型大小 | 中等 | 较小 |

### 检测流程

1. **视频读取**: 使用OpenCV读取视频文件
2. **帧采样**: 每3秒抽取一帧进行检测
3. **质量检查**: 检查帧的亮度和对比度
4. **图像预处理**: 裁剪为正方形区域
5. **人脸检测**: 使用RetinaFace进行检测
6. **结果过滤**: 过滤小尺寸和低置信度的检测
7. **可视化**: 绘制检测框、置信度和面部关键点
8. **结果保存**: 保存检测图像和拼接图像

## 📊 可视化说明

程序生成的图像包含以下可视化元素：

### 人脸检测
- **绿色矩形框**: 人脸边界框
- **Face ID + 置信度**: 人脸编号和检测置信度

### 面部特征点
- **蓝色圆点**: 眼睛位置（左眼、右眼）
- **黄色圆点**: 鼻子位置
- **红色圆点**: 嘴角位置（左嘴角、右嘴角）
- **白色边框**: 关键点轮廓

### 头部姿态可视化
- **红色线条**: X轴 (左右方向)
- **绿色线条**: Y轴 (上下方向)
- **蓝色线条**: Z轴 (前后方向)
- **黄色文字**: 姿态角度 (Yaw/Pitch/Roll)

### 偏离度显示
- **左上角大号黄色数字**: 人脸偏离度（度数）
- **黑色背景**: 提高数字可读性
- **实时更新**: 每帧都会更新偏离度数值

### 关键点说明

RetinaFace检测的5个面部关键点：
1. **左眼中心** - 蓝色圆点
2. **右眼中心** - 蓝色圆点
3. **鼻尖** - 黄色圆点
4. **左嘴角** - 红色圆点
5. **右嘴角** - 红色圆点

## 性能优化

- **智能采样**: 避免处理每一帧，大幅提升处理速度
- **质量预检**: 跳过低质量帧，减少无效检测
- **尺寸过滤**: 过滤过小的检测框，提高结果质量
- **批量处理**: 支持大规模视频文件处理
- **内存管理**: 及时释放资源，避免内存泄漏

## 注意事项

1. **首次运行**: 程序首次运行时会自动下载RetinaFace模型（约几百MB）
2. **GPU支持**: 如果有GPU，程序会自动使用GPU加速
3. **存储空间**: 确保输出目录有足够的存储空间
4. **视频格式**: 支持常见的视频格式（mp4, avi, mov等）

## 故障排除

### 常见问题

1. **模型下载失败**: 检查网络连接，确保能访问ModelScope
2. **内存不足**: 减少并发处理的视频数量
3. **检测结果为空**: 检查视频质量和人脸大小

### 日志查看

```bash
# 查看成功处理的视频
cat output/success.log

# 查看错误信息
cat output/error.log
```

## 🎉 更新历史

- **v2.0**: 集成Hopenet头部姿态估计功能
  - 添加3D坐标轴可视化
  - 添加人脸偏离度计算和显示
  - 保持原有所有功能
  - 通过完整测试验证

- **v1.0**: 完成YOLO11到RetinaFace的迁移
  - 保持原有功能的同时提升检测精度
  - 优化代码结构和错误处理
  - 添加详细的日志和进度显示

## 📈 性能对比

| 功能 | 原版RetinaFace | 增强版RetinaFace |
|------|----------------|------------------|
| 人脸检测 | ✅ | ✅ |
| 面部特征点 | ✅ | ✅ |
| 头部姿态估计 | ❌ | ✅ |
| 3D坐标轴可视化 | ❌ | ✅ |
| 偏离度计算 | ❌ | ✅ |
| 处理速度 | 快 | 中等（增加了姿态估计） |
| 信息丰富度 | 丰富 | 非常丰富 |

---

**开发完成**: 2025年6月9日
**测试状态**: ✅ 已通过完整测试
**兼容性**: Python 3.8+, Linux/Windows/macOS
**GPU支持**: CUDA加速（可选）
