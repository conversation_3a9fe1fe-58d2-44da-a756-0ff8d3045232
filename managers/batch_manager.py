#!/usr/bin/env python3
"""
批处理管理器模块
"""

import time
from tqdm import tqdm

from ..processors.video_processor import VideoProcessor
from ..processors.face_processor import FaceProcessor
from ..services.file_service import FileService
from ..services.logging_service import LoggingService
from ..handlers.result_handler import ResultHandler
from ..handlers.visualization_handler import VisualizationHandler


class BatchManager:
    """批处理管理器类"""

    def __init__(self, enable_hopenet=False):
        """
        初始化批处理管理器
        
        Args:
            enable_hopenet (bool): 是否启用HopeNet
        """
        self.video_processor = VideoProcessor()
        self.face_processor = FaceProcessor(enable_hopenet=enable_hopenet)
        self.result_handler = ResultHandler()
        self.visualization_handler = VisualizationHandler()
        
        print("✓ 批处理管理器初始化完成")

    def find_first_valid_geometry_face(self, video_path: str, save_image_path: str) -> str:
        """
        在视频中找到第一个有效几何形状的人脸
        
        Args:
            video_path (str): 视频文件路径
            save_image_path (str): 保存图像路径
            
        Returns:
            str: 时间戳字符串 "HH:MM:SS"
        """
        attempt_count = 0
        print("开始几何形状验证")

        try:
            for frame, frame_index, fps, total_frames in self.video_processor.extract_frames(video_path):
                # 检查帧质量
                if not self.video_processor.check_frame_quality(frame):
                    continue

                start_time = time.time()

                # 准备帧进行检测
                cropped_frame, x_offset, y_offset, h, w = self.video_processor.prepare_frame_for_detection(frame)

                # 检测人脸
                detection_result = self.face_processor.detect_faces(cropped_frame)

                # 过滤人脸
                filtered_faces = self.face_processor.filter_faces(detection_result, w, h)

                # 检查是否应该保存中间结果
                if self.result_handler.should_save_intermediate(filtered_faces):
                    attempt_count += 1
                    inference_time = (time.time() - start_time) * 1000

                    # 找到最佳几何形状的人脸
                    best_face_result = self.face_processor.find_best_geometry_face(filtered_faces)

                    if best_face_result:
                        face_info = best_face_result['face']
                        geometry_score = best_face_result['geometry_score']
                        
                        # 验证几何形状
                        is_valid_quad, nose_inside = self.face_processor.validate_face_geometry(face_info['landmarks'])

                        # 估计头部姿态
                        adjusted_bbox = self.visualization_handler.draw_face_detection_result(
                            frame, face_info, x_offset, y_offset
                        )
                        pose_angles = self.face_processor.estimate_head_pose(frame, adjusted_bbox)
                        deviation = self.face_processor.calculate_face_deviation(pose_angles)

                        # 绘制完整的可视化结果
                        self.visualization_handler.draw_complete_result(
                            frame, face_info, x_offset, y_offset, is_valid_quad, nose_inside,
                            inference_time, attempt_count, geometry_score, pose_angles, deviation
                        )

                        # 检查是否找到有效几何形状
                        if self.result_handler.is_geometry_valid(geometry_score):
                            timestamp = self.video_processor.calculate_timestamp(frame_index, fps)
                            self.result_handler.save_final_result(frame, save_image_path, timestamp, attempt_count)
                            return timestamp
                        else:
                            # 保存中间结果
                            self.result_handler.save_intermediate_result(frame, save_image_path, attempt_count, geometry_score)

        except Exception as e:
            print(f"❌ 处理视频时出错: {e}")
            return "00:00:00"

        print(f"❌ 视频结束，未找到有效几何形状。总尝试次数: {attempt_count}")
        return "00:00:00"

    def process_videos(self, input_base: str, output_base: str):
        """
        批量处理视频文件
        
        Args:
            input_base (str): 输入基础路径
            output_base (str): 输出基础路径
        """
        # 初始化服务
        file_service = FileService()
        logging_service = LoggingService(output_base)

        # 收集视频文件
        video_paths = file_service.collect_video_files(input_base)
        
        # 确保输出目录存在
        file_service.ensure_directory_exists(output_base)

        # 加载已处理的文件
        processed_file = logging_service.get_processed_file_path()
        processed_set = file_service.load_processed_files(processed_file)

        # 过滤掉已处理的视频
        pending_paths = [p for p in video_paths if p not in processed_set]

        # 处理视频
        for input_path in tqdm(pending_paths, desc="Processing videos with geometry validation", unit="file"):
            try:
                # 获取输出路径
                output_dir, output_jpg = file_service.get_output_path(input_path, input_base, output_base)
                file_service.ensure_directory_exists(output_dir)

                # 处理视频
                timestamp = self.find_first_valid_geometry_face(input_path, output_jpg)
                
                # 标记为已处理
                file_service.mark_file_as_processed(processed_file, input_path)
                
                # 记录成功
                logging_service.log_success(f"Successfully processed: {input_path}")
                
                if timestamp != "00:00:00":
                    print(f"✓ 找到有效几何形状的时间戳：{timestamp}")
                else:
                    print(f"⚠️ 未找到有效几何形状")

            except Exception as e:
                error_msg = f"Failed processing '{input_path}'. Error: {e}"
                logging_service.log_error(error_msg)
                print(f"❌ {error_msg}")
