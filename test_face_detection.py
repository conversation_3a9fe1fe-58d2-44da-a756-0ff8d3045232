#!/usr/bin/env python3
"""
测试人脸检测功能
"""

import cv2
import numpy as np
import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_face_detection():
    """测试人脸检测功能"""
    
    print("测试人脸检测功能...")
    
    # 创建测试目录
    os.makedirs('test_data', exist_ok=True)
    
    # 创建一个简单的测试图像
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    img.fill(128)  # 灰色背景
    
    # 添加一些简单的形状作为"人脸"
    cv2.rectangle(img, (200, 150), (400, 350), (255, 255, 255), -1)  # 白色矩形作为脸
    cv2.circle(img, (250, 200), 10, (0, 0, 0), -1)  # 左眼
    cv2.circle(img, (350, 200), 10, (0, 0, 0), -1)  # 右眼
    cv2.circle(img, (300, 250), 5, (0, 0, 0), -1)   # 鼻子
    cv2.circle(img, (280, 300), 5, (0, 0, 0), -1)   # 左嘴角
    cv2.circle(img, (320, 300), 5, (0, 0, 0), -1)   # 右嘴角
    
    # 保存测试图像
    test_image_path = 'test_data/test_image.jpg'
    cv2.imwrite(test_image_path, img)
    print(f"✓ 测试图像已保存: {test_image_path}")
    
    # 测试RetinaFace检测器
    try:
        from models.pipeline import RetinaFaceDetectionPipeline
        
        print("正在初始化RetinaFace检测器...")
        model_path = 'models/cv_resnet50_face-detection_retinaface'
        device = 'cpu'  # 使用CPU模式以避免GPU问题
        detector = RetinaFaceDetectionPipeline(model_path, device=device)
        print("✓ 检测器初始化成功")
        
        # 进行检测
        print("正在进行人脸检测...")
        result = detector(img)
        print("✓ 检测完成")
        
        print(f"检测结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"结果键: {list(result.keys())}")
            
            scores = result.get('scores', [])
            boxes = result.get('boxes', [])
            keypoints = result.get('keypoints', [])
            
            print(f"检测到 {len(scores)} 个人脸")
            for i, (score, box, kp) in enumerate(zip(scores, boxes, keypoints)):
                print(f"  人脸 {i+1}: 置信度={score:.3f}, 边界框={box}")
                if kp:
                    print(f"    关键点: {kp[:10]}...")  # 只显示前10个坐标
        
        # 可视化结果
        result_img = img.copy()
        if isinstance(result, dict):
            scores = result.get('scores', [])
            boxes = result.get('boxes', [])
            keypoints = result.get('keypoints', [])
            
            for i, (score, box, kp) in enumerate(zip(scores, boxes, keypoints)):
                if score > 0.5:  # 只显示高置信度的检测
                    x1, y1, x2, y2 = map(int, box)
                    cv2.rectangle(result_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(result_img, f'{score:.2f}', (x1, y1-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                    
                    # 绘制关键点
                    if kp and len(kp) >= 10:
                        for j in range(0, 10, 2):
                            cv2.circle(result_img, (int(kp[j]), int(kp[j+1])), 3, (255, 0, 0), -1)
        
        # 保存结果图像
        result_path = 'test_data/detection_result.jpg'
        cv2.imwrite(result_path, result_img)
        print(f"✓ 检测结果已保存: {result_path}")
        
    except Exception as e:
        print(f"❌ 人脸检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    success = test_face_detection()
    if success:
        print("\n" + "=" * 60)
        print("✓ 人脸检测功能测试成功！")
        print("✓ 依赖替换验证完成：本地RetinaFace模型工作正常")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 人脸检测功能测试失败")
        print("=" * 60)
