#!/usr/bin/env python3
"""
测试模块化人脸分析器
"""

import sys
import os
import cv2
import numpy as np

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from models.pipeline import RetinaFaceDetectionPipeline
        print("✓ 本地RetinaFace模型导入成功")
    except Exception as e:
        print(f"❌ 本地RetinaFace模型导入失败: {e}")
        return False
    
    try:
        from face_analyzer.core.face_detector import RetinaFaceDetector
        print("✓ 人脸检测器导入成功")
    except Exception as e:
        print(f"❌ 人脸检测器导入失败: {e}")
        return False
    
    try:
        from face_analyzer.core.geometry_validator import FaceGeometryValidator
        print("✓ 几何验证器导入成功")
    except Exception as e:
        print(f"❌ 几何验证器导入失败: {e}")
        return False
    
    try:
        from face_analyzer.batch_processor import BatchProcessor
        print("✓ 批处理器导入成功")
    except Exception as e:
        print(f"❌ 批处理器导入失败: {e}")
        return False
    
    return True

def test_detector():
    """测试检测器初始化"""
    print("\n测试检测器初始化...")
    
    try:
        from face_analyzer.core.face_detector import RetinaFaceDetector
        detector = RetinaFaceDetector()
        print("✓ 检测器初始化成功")
        return detector
    except Exception as e:
        print(f"❌ 检测器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_geometry_validator():
    """测试几何验证器"""
    print("\n测试几何验证器...")
    
    try:
        from face_analyzer.core.geometry_validator import FaceGeometryValidator
        validator = FaceGeometryValidator()
        print("✓ 几何验证器初始化成功")
        return validator
    except Exception as e:
        print(f"❌ 几何验证器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_test_image():
    """创建测试图像"""
    # 创建一个简单的测试图像
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    img.fill(128)  # 灰色背景
    
    # 添加一些简单的形状作为"人脸"
    cv2.rectangle(img, (200, 150), (400, 350), (255, 255, 255), -1)  # 白色矩形作为脸
    cv2.circle(img, (250, 200), 10, (0, 0, 0), -1)  # 左眼
    cv2.circle(img, (350, 200), 10, (0, 0, 0), -1)  # 右眼
    cv2.circle(img, (300, 250), 5, (0, 0, 0), -1)   # 鼻子
    cv2.circle(img, (280, 300), 5, (0, 0, 0), -1)   # 左嘴角
    cv2.circle(img, (320, 300), 5, (0, 0, 0), -1)   # 右嘴角
    
    return img

def main():
    """主测试函数"""
    print("=" * 60)
    print("模块化人脸分析器测试")
    print("=" * 60)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，退出")
        return
    
    # 测试检测器
    detector = test_detector()
    if detector is None:
        print("❌ 检测器测试失败，退出")
        return
    
    # 测试几何验证器
    validator = test_geometry_validator()
    if validator is None:
        print("❌ 几何验证器测试失败，退出")
        return
    
    print("\n" + "=" * 60)
    print("✓ 所有模块测试通过！")
    print("模块化重构成功完成")
    print("=" * 60)

if __name__ == '__main__':
    main()
