import os
import argparse
import logging
from tqdm import tqdm
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
import json
import subprocess
import shutil
from contextlib import contextmanager
import tempfile
import cv2

# Supported video extensions
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

# 全局加载 YuNet 轻量模型
MODEL_PATH = "/home/<USER>/modelscope/face_detection_yunet_2023mar.onnx"
# 输入尺寸、置信度与 NMS 阈值可根据场景适当调节
INPUT_SIZE = (320, 320)
SCORE_THRESHOLD = 0.8
NMS_THRESHOLD = 0.3
TOP_K = 5000

face_detector = cv2.FaceDetectorYN.create(
    model=MODEL_PATH,
    config="",
    input_size=INPUT_SIZE,
    score_threshold=SCORE_THRESHOLD,
    nms_threshold=NMS_THRESHOLD,
    top_k=TOP_K,
    backend_id=cv2.dnn.DNN_BACKEND_DEFAULT,
    target_id=cv2.dnn.DNN_TARGET_CPU
)

@contextmanager
def managed_temp_dir(temp_dir: str):
    """
    上下文管理器：使用完毕后自动删除 temp_dir 及其内容。
    """
    try:
        yield temp_dir
    finally:
        # 递归删除目录及所有文件
        shutil.rmtree(temp_dir, ignore_errors=True)  # :contentReference[oaicite:0]{index=0}

def parse_timestamp(ts):
    """
    将以下形式的时间戳转换为秒（float）：
      - 整数秒：           33
      - 带冒号的：         "00:00:33"
      - 带毫秒的字符串：   "00:00:33.033"
    """
    if isinstance(ts, (int, float)):
        return float(ts)
    # ts 可能是 "HH:MM:SS" 或 "HH:MM:SS.mmm"
    parts = ts.split(':')
    if len(parts) == 3:
        h = int(parts[0])
        m = int(parts[1])
        # 最后一部分可能带小数点
        sec = float(parts[2])
        return h * 3600 + m * 60 + sec
    else:
        # 其它格式（如 "33.033"）直接转 float
        return float(ts)

def get_frame_at_timestamp(cap, timestamp, seek_frame=True):
    """
    从视频中读取指定时间戳（可含毫秒）的那一帧，返回 (ret, frame)：
      - ret=True, frame=ndarray    表示成功读取
      - ret=False, frame=None      表示读取失败或超出时长
    """
    secs = parse_timestamp(timestamp)
    if seek_frame:
        # 将秒转为毫秒后定位
        cap.set(cv2.CAP_PROP_POS_MSEC, secs * 1000.0)
    else:
        # 按帧数定位
        fps = cap.get(cv2.CAP_PROP_FPS) or 25.0
        cap.set(cv2.CAP_PROP_POS_FRAMES, int(secs * fps))

    ret, frame = cap.read()
    return frame

def handle_face_detection(segments, clip_file) -> str:
    cap = cv2.VideoCapture(clip_file)
    if not cap.isOpened():
        raise IOError(f"无法打开视频文件：{clip_file}")
    
    for seg in segments:
        ts = seg['timestamps'][0]
        frame = get_frame_at_timestamp(cap, ts)
        # 更新检测器输入尺寸
        h, w = frame.shape[:2]
        face_detector.setInputSize((w, h))

        # 前向推理
        _, faces = face_detector.detect(frame)
        if faces is not None and len(faces) > 0:
            best_score = max(faces[:, 14])
            if best_score >= SCORE_THRESHOLD:
                cap.release()
                return ts, frame, True

    ts = segments[0]['timestamps'][0]
    frame = get_frame_at_timestamp(cap, ts)
    cap.release()
    return ts, frame, False

def extract_first_120_seconds(video_path: str, temp_dir: str) -> str:
    """
    从 video_path 指定的视频中提取前 120 秒到 temp_dir，并返回输出文件路径。

    :param video_path: 源视频文件路径
    :param temp_dir: 外部传入的临时目录，不在此函数内创建
    :return: 截取视频的文件路径
    :raises FileNotFoundError: 如果源文件不存在
    :raises subprocess.CalledProcessError: 如果 FFmpeg 执行失败
    """
    if not os.path.isfile(video_path):
        raise FileNotFoundError(f"视频文件不存在：{video_path}")  # :contentReference[oaicite:1]{index=1}

    # 确保临时目录存在
    os.makedirs(temp_dir, exist_ok=True)  # :contentReference[oaicite:2]{index=2}

    base, ext = os.path.splitext(os.path.basename(video_path))
    output_path = os.path.join(temp_dir, f"{base}_first120{ext}")

    # 构造 FFmpeg 命令：-ss 0 从 0 秒开始；-t 120 持续 120 秒；-c copy 流复制模式极速截取 :contentReference[oaicite:3]{index=3}
    cmd = [
        "ffmpeg",
        "-loglevel", "error",  # 仅输出错误，抑制无关日志
        "-ss", "0",
        "-t", "120",
        "-i", video_path,
        "-c", "copy",
        output_path
    ]

    # 执行 FFmpeg
    subprocess.run(cmd, check=True)  # :contentReference[oaicite:4]{index=4}

    return output_path


def process_videos(input_base: str, output_base: str):
    summarization_pipeline = pipeline(Tasks.video_summarization, model='iic/cv_googlenet_pgl-video-summarization')
    # Collect all video files first
    video_paths = []
    for root, _, files in os.walk(input_base):
        for fname in files:
            if fname.lower().endswith(VIDEO_EXTENSIONS):
                video_paths.append(os.path.join(root, fname))

    # Ensure base output directory exists
    os.makedirs(output_base, exist_ok=True)

    # Path for record of processed files
    processed_file = os.path.join(output_base, 'processed.log')
    # Load already processed files
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # Filter out already processed videos
    pending_paths = [p for p in video_paths if p not in processed_set]

    # Setup separate loggers for success and error
    success_log = os.path.join(output_base, 'success.log')
    error_log = os.path.join(output_base, 'error.log')

    # Error logger
    error_logger = logging.getLogger('whisperx_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    # Success logger
    success_logger = logging.getLogger('whisperx_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    # Process with progress bar
    for input_path in tqdm(pending_paths, desc="Processing videos", unit="file"):
        # Determine output directory based on relative path and filename
        root = os.path.dirname(input_path)
        fname = os.path.basename(input_path)
        rel_dir = os.path.relpath(root, input_base)
        base_name, _ = os.path.splitext(fname)
        output_dir = os.path.join(output_base, rel_dir)
        output_json = os.path.join(output_dir, base_name+".json")
        os.makedirs(output_dir, exist_ok=True)

        try:
            temp_dir = tempfile.mkdtemp(prefix="clip_")
            with managed_temp_dir(temp_dir) as td:
                clip_file = extract_first_120_seconds(input_path, td)
                result = summarization_pipeline(clip_file)
                # 处理人脸检测
                ts, frame, status = handle_face_detection(result['output'], clip_file)

                # 保存当前帧
                output_img = os.path.join(output_dir, f"{base_name}_{ts}.jpg")
                cv2.imwrite(output_img, frame)
                with open(output_json, "w") as f:
                    json.dump(result,f)

        except Exception as e:
            error_logger.error(
                f"Failed processing '{input_path}'. Return: {e}\n"
            )
        else:
            if status == False:
                error_logger.error(
                    f"Failed to detect face in {input_path}"
                )
                continue
            # Log success and mark as processed
            success_logger.info(f"Successfully processed: {input_path}")
            with open(processed_file, 'a') as pf:
                pf.write(f"{input_path}\n")


def main():
    parser = argparse.ArgumentParser(
        description='Batch process videos with whisperx across a directory tree.'
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )

    args = parser.parse_args()
    process_videos(args.input_base, args.output_base)


if __name__ == '__main__':
    main()
