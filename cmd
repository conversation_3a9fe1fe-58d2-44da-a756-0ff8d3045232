batch_retinaface_geometry.py是一个从视频中分析并提取人脸帧的程序，我要求基于该程序，在face_thumbnails文件夹下创造一个新程序，具体要求如下：
* 保留原始程序的扫描文件夹下视频文件功能和人脸分析算法，
* 对于每个被扫描的视频文件，分割为TARGET_FACE_FRAMES份，每份并行地分析人脸直至获得第一个符合条件的帧，将这些帧制作为Y秒的webp缩略图视频（具体取值应作为全局变量，目前取X=10，Y=5）
* 

设计算法，从视频中提取X个人脸帧，这些帧应尽可能地分散在整段视频中，且应保证数量满足要求；2. 将这些帧制作为Y秒的webp缩略图视频（具体取值应作为全局变量，放在文件开头，目前取X=10，Y=5）。测试文件可以在~/video_summerization/test 下获得

这是一个从视频中抽取人脸帧，组合成缩略视频的程序，请参照batch_yolo.py，将其改造为能从一个文件夹下批量扫描并处理所有视频文件的批处理程序

请进一步改进程序逻辑。原来的程序是每间隔一定长度SAMPLE_INTERVAL_SEC，提取一帧。为了使采样帧更加均匀分布，需要将采样逻辑改造成：将视频分割为TARGET_FACE_FRAMES份，每份并行地分析人脸直至获得第一个符合条件的帧，作为拼接缩略视频的图像来源。此外，遵守以下指示：1. 若某段视频没有符合条件的帧，则向后或向前借一个符合条件的帧。2. MIN_FACE_CONFIDENCE 设置为0.97。

你需要修改代码逻辑，使这TARGET_FACE_FRAMES个段能并行地进行分析