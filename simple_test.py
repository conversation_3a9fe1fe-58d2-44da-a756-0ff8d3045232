#!/usr/bin/env python3
"""
简单测试脚本
"""

import sys
import os

print("开始测试...")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")

# 测试基本导入
try:
    import torch
    print(f"✓ PyTorch版本: {torch.__version__}")
    print(f"✓ CUDA可用: {torch.cuda.is_available()}")
except Exception as e:
    print(f"❌ PyTorch导入失败: {e}")

try:
    import cv2
    print(f"✓ OpenCV版本: {cv2.__version__}")
except Exception as e:
    print(f"❌ OpenCV导入失败: {e}")

try:
    import numpy as np
    print(f"✓ NumPy版本: {np.__version__}")
except Exception as e:
    print(f"❌ NumPy导入失败: {e}")

# 测试本地模型导入
print("\n测试本地模型...")
try:
    from models.utils import Config
    print("✓ 本地utils导入成功")
except Exception as e:
    print(f"❌ 本地utils导入失败: {e}")

try:
    from models.retinaface.detection import RetinaFaceDetection
    print("✓ RetinaFace检测器导入成功")
except Exception as e:
    print(f"❌ RetinaFace检测器导入失败: {e}")

print("\n基本测试完成")
