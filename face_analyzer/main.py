#!/usr/bin/env python3
"""
人脸几何形状验证主程序
"""

import argparse
from .batch_processor import BatchProcessor
from .utils.visualization import print_visualization_legend
from .config.settings import MAX_GEOMETRY_ATTEMPTS


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Batch process videos with RetinaFace face detection and geometry validation.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
几何验证说明:
  本程序会验证人脸的几何形状，包括：
  1. 检查左眼-右眼-右嘴角-左嘴角是否构成有效四边形（无自相交）
  2. 检查鼻子是否在上述四边形内部

  只有当两个条件都满足时，才会保存最终结果图片。
  在找到有效几何形状之前，会保存中间结果（文件名带_1, _2等后缀）。

HopeNet功能说明:
  HopeNet人脸朝向检测功能默认禁用，可通过以下方式启用：
  1. 使用 --enable-hopenet 命令行参数
  2. 在配置中将 ENABLE_HOPENET 设置为 True

示例:
  python -m face_analyzer.main input_dir output_dir
  python -m face_analyzer.main input_dir output_dir --enable-hopenet --max-attempts 5
        """
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )
    parser.add_argument(
        '--max-attempts',
        type=int,
        default=MAX_GEOMETRY_ATTEMPTS,
        help=f'Maximum number of attempts to find valid geometry (default: {MAX_GEOMETRY_ATTEMPTS})'
    )
    parser.add_argument(
        '--enable-hopenet',
        action='store_true',
        help='Enable HopeNet head pose estimation (disabled by default)'
    )

    args = parser.parse_args()

    # 更新最大尝试次数
    import face_analyzer.config.settings as settings
    settings.MAX_GEOMETRY_ATTEMPTS = args.max_attempts

    # 根据命令行参数更新HopeNet设置
    enable_hopenet = args.enable_hopenet
    if enable_hopenet:
        print("✓ 通过命令行参数启用HopeNet功能")

    print("=" * 80)
    if enable_hopenet:
        print("RetinaFace + HopeNet 人脸几何形状验证与朝向分析程序")
    else:
        print("RetinaFace 人脸几何形状验证程序")
    print("=" * 80)
    print(f"输入目录: {args.input_base}")
    print(f"输出目录: {args.output_base}")
    print(f"最大尝试次数: {args.max_attempts}")
    print(f"几何验证: 启用")
    print(f"HopeNet功能: {'启用' if enable_hopenet else '禁用'}")

    # 显示可视化说明
    print_visualization_legend(enable_hopenet)

    # 创建批处理器并处理视频
    processor = BatchProcessor(enable_hopenet=enable_hopenet)
    processor.process_videos(args.input_base, args.output_base)

    print("=" * 80)
    print("批量处理完成！")
    print("=" * 80)

    # 再次显示可视化说明
    print_visualization_legend(enable_hopenet)


if __name__ == '__main__':
    main()
