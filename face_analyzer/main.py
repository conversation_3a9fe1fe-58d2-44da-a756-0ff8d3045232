#!/usr/bin/env python3
"""
人脸几何形状验证主程序
"""

import argparse
import sys
import os

# 添加项目根目录到Python路径，以便正确导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .batch_processor import BatchProcessor
    from .utils.visualization import print_visualization_legend
    from .config.settings import ENABLE_HOPENET
except ImportError:
    # 如果相对导入失败，使用绝对导入
    from face_analyzer.batch_processor import BatchProcessor
    from face_analyzer.utils.visualization import print_visualization_legend
    from face_analyzer.config.settings import ENABLE_HOPENET

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Batch process videos with RetinaFace face detection and geometry validation.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python face_analyzer/main.py input_dir output_dir
  或者
  python -m face_analyzer.main input_dir output_dir
        """
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )

    args = parser.parse_args()

    print("=" * 80)
    print(f"几何验证: 启用")
    print(f"HopeNet功能: {'启用' if ENABLE_HOPENET else '禁用'}")

    # 显示可视化说明
    print_visualization_legend(ENABLE_HOPENET)

    # 创建批处理器并处理视频
    processor = BatchProcessor(enable_hopenet=ENABLE_HOPENET)
    processor.process_videos(args.input_base, args.output_base)

    print("=" * 80)
    print("批量处理完成！")
    print("=" * 80)

    # 再次显示可视化说明
    print_visualization_legend(ENABLE_HOPENET)


if __name__ == '__main__':
    main()
