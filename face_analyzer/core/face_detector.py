#!/usr/bin/env python3
"""
RetinaFace人脸检测器模块
"""

import torch
import sys
import os

# 添加项目根目录到Python路径以便导入models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.retinaface.detection import RetinaFaceDetection
from ..config.settings import SCORE_THRESHOLD, RETINAFACE_MODEL_PATH, DEVICE


def create_retinaface_detector(model_path=None, device=None):
    """
    创建RetinaFace检测器

    Args:
        model_path (str): 模型路径，默认使用配置中的路径
        device (str): 设备，默认使用配置中的设备

    Returns:
        RetinaFaceDetection: 检测器实例
    """
    model_path = model_path or f"{RETINAFACE_MODEL_PATH}/pytorch_model.pt"
    device = device or DEVICE

    print("正在初始化RetinaFace模型...")
    detector = RetinaFaceDetection(model_path, device=device)
    print("✓ RetinaFace模型初始化完成")

    return detector

