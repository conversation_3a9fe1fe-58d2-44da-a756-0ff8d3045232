#!/usr/bin/env python3
"""
RetinaFace人脸检测器模块
"""

import torch
import sys
import os

# 添加项目根目录到Python路径以便导入models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.retinaface.detection import RetinaFaceDetection
from ..config.settings import SCORE_THRESHOLD, RETINAFACE_MODEL_PATH, DEVICE


def create_retinaface_detector(model_path=None, device=None):
    """
    创建RetinaFace检测器

    Args:
        model_path (str): 模型路径，默认使用配置中的路径
        device (str): 设备，默认使用配置中的设备

    Returns:
        RetinaFaceDetection: 检测器实例
    """
    model_path = model_path or f"{RETINAFACE_MODEL_PATH}/pytorch_model.pt"
    device = device or DEVICE

    print("正在初始化RetinaFace模型...")
    detector = RetinaFaceDetection(model_path, device=device)
    print("✓ RetinaFace模型初始化完成")

    return detector


def parse_retinaface_result(result, score_threshold=None):
    """
    解析RetinaFace检测结果

    Args:
        result (tuple): RetinaFaceDetection返回的结果 (dets, landms)
        score_threshold (float): 置信度阈值

    Returns:
        list: 解析后的人脸信息列表
    """
    score_threshold = score_threshold or SCORE_THRESHOLD
    faces = []

    if result is None or len(result) != 2:
        return faces

    dets, landms = result

    if dets is None or len(dets) == 0:
        return faces

    for i in range(len(dets)):
        det = dets[i]
        if len(det) < 5:
            continue

        x1, y1, x2, y2, confidence = det[:5]

        if confidence < score_threshold:
            continue

        # 获取对应的关键点
        landmarks = None
        if landms is not None and i < len(landms):
            landmarks = landms[i]

        face_info = {
            'id': i + 1,
            'confidence': confidence,
            'bbox': [x1, y1, x2, y2],
            'landmarks': landmarks
        }
        faces.append(face_info)

    return faces

def filter_faces_by_size(faces, min_size=30, min_ratio=0.1, image_width=None, image_height=None):
    """
    根据尺寸过滤人脸

    Args:
        faces (list): 人脸列表
        min_size (int): 最小像素尺寸
        min_ratio (float): 最小占画面比例
        image_width (int): 图像宽度
        image_height (int): 图像高度

    Returns:
        list: 过滤后的人脸列表
    """
    filtered_faces = []

    for face in faces:
        bbox = face['bbox']
        x1, y1, x2, y2 = bbox
        width = x2 - x1
        height = y2 - y1

        # 检查最小像素尺寸
        if width <= min_size or height <= min_size:
            continue

        # 检查最小占画面比例（如果提供了图像尺寸）
        if image_width and image_height:
            width_ratio = width / image_width
            height_ratio = height / image_height
            if width_ratio < min_ratio or height_ratio < min_ratio:
                continue

        filtered_faces.append(face)

    return filtered_faces


def get_best_face(faces, criteria='confidence'):
    """
    获取最佳人脸

    Args:
        faces (list): 人脸列表
        criteria (str): 选择标准 ('confidence', 'size', 'center')

    Returns:
        dict: 最佳人脸信息，如果没有人脸则返回None
    """
    if not faces:
        return None

    if criteria == 'confidence':
        return max(faces, key=lambda f: f['confidence'])
    elif criteria == 'size':
        def face_size(face):
            bbox = face['bbox']
            return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
        return max(faces, key=face_size)
    elif criteria == 'center':
        # 选择最接近图像中心的人脸（需要图像尺寸信息）
        # 这里简化为选择置信度最高的
        return max(faces, key=lambda f: f['confidence'])
    else:
        return faces[0]
