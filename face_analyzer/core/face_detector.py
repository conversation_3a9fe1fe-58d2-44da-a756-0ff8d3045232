#!/usr/bin/env python3
"""
RetinaFace人脸检测器模块
"""

import torch
from models.pipeline import RetinaFaceDetectionPipeline
from ..config.settings import SCORE_THRESHOLD, RETINAFACE_MODEL_PATH, DEVICE


class RetinaFaceDetector:
    """RetinaFace人脸检测器类"""

    def __init__(self, model_path=None, device=None, score_threshold=None):
        """
        初始化检测器
        
        Args:
            model_path (str): 模型路径，默认使用配置中的路径
            device (str): 设备，默认使用配置中的设备
            score_threshold (float): 置信度阈值，默认使用配置中的阈值
        """
        self.model_path = model_path or RETINAFACE_MODEL_PATH
        self.device = device or DEVICE
        self.score_threshold = score_threshold or SCORE_THRESHOLD
        
        print("正在初始化RetinaFace模型...")
        self.detector = RetinaFaceDetectionPipeline(self.model_path, device=self.device)
        print("✓ RetinaFace模型初始化完成")

    def detect_faces_from_array(self, image_array):
        """
        从numpy数组检测人脸

        Args:
            image_array (numpy.ndarray): 图像数组

        Returns:
            dict: 检测结果
        """
        # 直接使用numpy数组进行检测，避免临时文件
        result = self.detector(image_array)
        return result

    def parse_result(self, result):
        """
        解析检测结果

        Args:
            result (dict): 原始检测结果

        Returns:
            list: 解析后的人脸信息列表
        """
        faces = []

        scores = result.get('scores', [])
        boxes = result.get('boxes', [])
        keypoints = result.get('keypoints', [])

        for i in range(len(scores)):
            if scores[i] < self.score_threshold:
                continue
            face_info = {
                'id': i + 1,
                'confidence': scores[i],
                'bbox': boxes[i],  # [x1, y1, x2, y2]
                'landmarks': keypoints[i] if i < len(keypoints) else None
            }
            faces.append(face_info)

        return faces

    def filter_faces_by_size(self, faces, min_size=30, min_ratio=0.1, image_width=None, image_height=None):
        """
        根据尺寸过滤人脸
        
        Args:
            faces (list): 人脸列表
            min_size (int): 最小像素尺寸
            min_ratio (float): 最小占画面比例
            image_width (int): 图像宽度
            image_height (int): 图像高度
            
        Returns:
            list: 过滤后的人脸列表
        """
        filtered_faces = []
        
        for face in faces:
            bbox = face['bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1
            
            # 检查最小像素尺寸
            if width <= min_size or height <= min_size:
                continue
                
            # 检查最小占画面比例（如果提供了图像尺寸）
            if image_width and image_height:
                width_ratio = width / image_width
                height_ratio = height / image_height
                if width_ratio < min_ratio or height_ratio < min_ratio:
                    continue
                    
            filtered_faces.append(face)
            
        return filtered_faces

    def get_best_face(self, faces, criteria='confidence'):
        """
        获取最佳人脸
        
        Args:
            faces (list): 人脸列表
            criteria (str): 选择标准 ('confidence', 'size', 'center')
            
        Returns:
            dict: 最佳人脸信息，如果没有人脸则返回None
        """
        if not faces:
            return None
            
        if criteria == 'confidence':
            return max(faces, key=lambda f: f['confidence'])
        elif criteria == 'size':
            def face_size(face):
                bbox = face['bbox']
                return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
            return max(faces, key=face_size)
        elif criteria == 'center':
            # 选择最接近图像中心的人脸（需要图像尺寸信息）
            # 这里简化为选择置信度最高的
            return max(faces, key=lambda f: f['confidence'])
        else:
            return faces[0]
