#!/usr/bin/env python3
"""
人脸处理器模块
"""

import torch
import sys
import os

# 添加项目根目录到Python路径以便导入models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.retinaface.detection import RetinaFaceDetection
from ..module.geometry_validator import FaceGeometryValidator
from ..module.hopenet_estimator import HopeNetEstimator
from ..config.settings import MIN_FACE_SIZE, MIN_FACE_RATIO, SCORE_THRESHOLD, RETINAFACE_MODEL_PATH, DEVICE


def create_retinaface_detector(model_path=None, device=None):
    """
    创建RetinaFace检测器

    Args:
        model_path (str): 模型路径，默认使用配置中的路径
        device (str): 设备，默认使用配置中的设备

    Returns:
        RetinaFaceDetection: 检测器实例
    """
    model_path = model_path or f"{RETINAFACE_MODEL_PATH}/pytorch_model.pt"
    device = device or DEVICE

    print("正在初始化RetinaFace模型...")
    detector = RetinaFaceDetection(model_path, device=device)
    print("✓ RetinaFace模型初始化完成")

    return detector


class FaceProcessor:
    """人脸处理器类"""

    def __init__(self, enable_hopenet=False):
        """
        初始化人脸处理器

        Args:
            enable_hopenet (bool): 是否启用HopeNet
        """
        self.detector = create_retinaface_detector()
        self.geometry_validator = FaceGeometryValidator()
        self.hopenet_estimator = HopeNetEstimator(enable=enable_hopenet)

    def detect_faces(self, frame):
        """
        检测人脸

        Args:
            frame: 输入帧

        Returns:
            list: 检测到的人脸列表
        """
        input_data = {'img': frame}
        result = self.detector(input_data)
        return result

    def filter_faces(self, detection_result, frame_width, frame_height):
        """
        过滤人脸检测结果

        Args:
            detection_result: 检测结果
            frame_width (int): 帧宽度
            frame_height (int): 帧高度

        Returns:
            list: 过滤后的人脸列表
        """
        filtered_faces = []

        if detection_result is not None and len(detection_result) == 2:
            dets, landms = detection_result

            if dets is not None and len(dets) > 0:
                for i in range(len(dets)):
                    det = dets[i]
                    if len(det) < 5:
                        continue

                    x1, y1, x2, y2, confidence = det[:5]

                    if confidence < 0.75:
                        continue

                    # 过滤掉长、宽小于30像素的检测框
                    width = x2 - x1
                    height = y2 - y1
                    if width <= MIN_FACE_SIZE or height <= MIN_FACE_SIZE:
                        continue

                    # 过滤掉长、宽小于画面10%的检测框
                    if width <= frame_width * MIN_FACE_RATIO or height <= frame_height * MIN_FACE_RATIO:
                        continue

                    # 获取对应的关键点
                    landmarks = None
                    if landms is not None and i < len(landms):
                        landmarks = landms[i]

                    if landmarks is not None:
                        face_info = {
                            'id': i + 1,
                            'confidence': confidence,
                            'bbox': [x1, y1, x2, y2],
                            'landmarks': landmarks
                        }
                        filtered_faces.append(face_info)

        return filtered_faces




