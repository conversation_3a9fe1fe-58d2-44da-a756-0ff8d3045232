#!/usr/bin/env python3
"""
人脸几何形状验证器模块
"""

from shapely.geometry import Point, Polygon
from shapely.validation import explain_validity
from ..config.settings import GEOMETRY_TOLERANCE


class FaceGeometryValidator:
    """人脸几何形状验证器"""

    def __init__(self, tolerance=None):
        """
        初始化验证器

        Args:
            tolerance (float): 几何计算容差
        """
        self.tolerance = tolerance or GEOMETRY_TOLERANCE
        print("✓ 人脸几何形状验证器初始化完成")

    def parse_landmarks(self, landmarks):
        """
        解析RetinaFace关键点

        Args:
            landmarks: RetinaFace输出的关键点列表 [x1, y1, x2, y2, ..., x5, y5]

        Returns:
            dict: 解析后的关键点字典
        """
        if landmarks is None or len(landmarks) < 10:
            return None

        # RetinaFace关键点顺序：左眼、右眼、鼻子、左嘴角、右嘴角
        points = {
            'left_eye': (landmarks[0], landmarks[1]),
            'right_eye': (landmarks[2], landmarks[3]),
            'nose': (landmarks[4], landmarks[5]),
            'left_mouth': (landmarks[6], landmarks[7]),
            'right_mouth': (landmarks[8], landmarks[9])
        }

        return points

    def check_quadrilateral_validity(self, points):
        """
        检查左眼-右眼-右嘴角-左嘴角-左眼是否构成单一封闭四边形

        Args:
            points (dict): 关键点字典

        Returns:
            bool: 是否构成有效四边形
        """
        try:
            # 构建四边形顶点序列：左眼 -> 右眼 -> 右嘴角 -> 左嘴角 -> 左眼
            quad_points = [
                points['left_eye'],
                points['right_eye'],
                points['right_mouth'],
                points['left_mouth']
            ]

            # 使用Shapely创建多边形
            polygon = Polygon(quad_points)

            # 检查多边形是否有效（无自相交、无重复点等）
            is_valid = polygon.is_valid

            if not is_valid:
                # 输出详细的无效原因（调试用）
                validity_reason = explain_validity(polygon)
                print(f"四边形无效: {validity_reason}")

            return is_valid

        except Exception as e:
            print(f"四边形验证错误: {e}")
            return False

    def check_nose_inside_quadrilateral(self, points):
        """
        检查鼻子是否在四边形内部

        Args:
            points (dict): 关键点字典

        Returns:
            bool: 鼻子是否在四边形内部
        """
        try:
            # 构建四边形
            quad_points = [
                points['left_eye'],
                points['right_eye'],
                points['right_mouth'],
                points['left_mouth']
            ]

            polygon = Polygon(quad_points)

            # 检查多边形是否有效
            if not polygon.is_valid:
                return False

            # 创建鼻子点
            nose_point = Point(points['nose'])

            # 检查鼻子是否在多边形内部（不包括边界）
            return polygon.contains(nose_point)

        except Exception as e:
            print(f"鼻子位置验证错误: {e}")
            return False

    def validate_face_geometry(self, landmarks):
        """
        验证人脸几何形状

        Args:
            landmarks: RetinaFace关键点

        Returns:
            tuple: (是否为有效四边形, 鼻子是否在内部)
        """
        points = self.parse_landmarks(landmarks)
        if not points:
            return False, False

        # 检查1: 四边形有效性
        is_valid_quad = self.check_quadrilateral_validity(points)

        # 检查2: 鼻子在内部
        nose_inside = False
        if is_valid_quad:
            nose_inside = self.check_nose_inside_quadrilateral(points)

        return is_valid_quad, nose_inside

    def calculate_geometry_score(self, landmarks):
        """
        计算几何评分

        Args:
            landmarks: RetinaFace关键点

        Returns:
            int: 几何评分 (0-2分)
        """
        is_valid_quad, nose_inside = self.validate_face_geometry(landmarks)
        return int(is_valid_quad) + int(nose_inside)

    def is_geometry_perfect(self, landmarks):
        """
        检查几何形状是否完美（两个条件都满足）

        Args:
            landmarks: RetinaFace关键点

        Returns:
            bool: 是否完美
        """
        return self.calculate_geometry_score(landmarks) == 2

    def is_geometry_valid(self, geometry_score: int) -> bool:
        """
        检查几何形状是否有效（从 result_handler 转移）

        Args:
            geometry_score (int): 几何评分

        Returns:
            bool: 几何形状是否有效（评分为2）
        """
        return geometry_score == 2

    def find_best_geometry_face(self, faces):
        """
        找到几何形状最佳的人脸（从 face_processor 转移）

        Args:
            faces (list): 人脸列表

        Returns:
            dict: 最佳人脸信息，包含face和geometry_score
        """
        if not faces:
            return None

        best_face = None
        best_geometry_score = -1

        for face in faces:
            landmarks = face['landmarks']
            if landmarks is not None:
                geometry_score = self.calculate_geometry_score(landmarks)

                if geometry_score > best_geometry_score:
                    best_geometry_score = geometry_score
                    best_face = {
                        'face': face,
                        'geometry_score': geometry_score
                    }

        return best_face

    def should_save_intermediate(self, faces, confidence_threshold: float = 0.75) -> bool:
        """
        判断是否应该保存中间结果（从 result_handler 转移）

        Args:
            faces (list): 人脸列表
            confidence_threshold (float): 置信度阈值

        Returns:
            bool: 是否应该保存中间结果
        """
        if not faces:
            return False

        max_confidence = max([face['confidence'] for face in faces])
        return max_confidence > confidence_threshold
