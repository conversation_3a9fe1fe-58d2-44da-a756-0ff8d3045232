#!/usr/bin/env python3
"""
日志服务模块
"""

import os
import logging


class LoggingService:
    """日志服务类"""

    def __init__(self, output_base: str):
        """
        初始化日志服务
        
        Args:
            output_base (str): 输出基础路径
        """
        self.output_base = output_base
        self.success_log = os.path.join(output_base, 'success_geometry.log')
        self.error_log = os.path.join(output_base, 'error_geometry.log')
        self.processed_file = os.path.join(output_base, 'processed_geometry.log')
        
        self._setup_loggers()

    def _setup_loggers(self):
        """设置日志记录器"""
        # 错误日志记录器
        self.error_logger = logging.getLogger('geometry_error')
        self.error_logger.setLevel(logging.ERROR)
        
        # 清除现有的处理器
        self.error_logger.handlers.clear()
        
        err_handler = logging.FileHandler(self.error_log)
        err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.error_logger.addHandler(err_handler)

        # 成功日志记录器
        self.success_logger = logging.getLogger('geometry_success')
        self.success_logger.setLevel(logging.INFO)
        
        # 清除现有的处理器
        self.success_logger.handlers.clear()
        
        succ_handler = logging.FileHandler(self.success_log)
        succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.success_logger.addHandler(succ_handler)

    def log_success(self, message: str):
        """
        记录成功日志
        
        Args:
            message (str): 日志消息
        """
        self.success_logger.info(message)

    def log_error(self, message: str):
        """
        记录错误日志
        
        Args:
            message (str): 错误消息
        """
        self.error_logger.error(message)

    def get_processed_file_path(self) -> str:
        """
        获取已处理文件记录路径
        
        Returns:
            str: 已处理文件记录路径
        """
        return self.processed_file
