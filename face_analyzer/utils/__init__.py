#!/usr/bin/env python3
"""
工具模块
"""

# 使用try-except来处理相对导入问题
try:
    from .image_utils import *
    from .visualization import *
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from face_analyzer.utils.image_utils import *
        from face_analyzer.utils.visualization import *
    except ImportError:
        # 如果都失败了，至少不要崩溃
        pass

__all__ = [
    'check_brightness_and_contrast',
    'crop_square_center',
    'draw_face_bbox',
    'draw_face_landmarks',
    'draw_face_quadrilateral',
    'draw_geometry_validation_result',
    'draw_axis',
    'draw_pose_info',
    'draw_processing_info',
    'draw_face_detection_result',
    'draw_complete_result',
    'print_visualization_legend'
]
