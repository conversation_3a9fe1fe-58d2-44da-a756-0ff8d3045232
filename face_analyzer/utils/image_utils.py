#!/usr/bin/env python3
"""
图像处理工具模块
"""

import cv2
import torch
import cvcuda
import numpy as np


def check_brightness_and_contrast(img):
    """
    检查图像亮度和对比度

    Args:
        img (numpy.ndarray): 输入图像

    Returns:
        tuple: (亮度, 对比度标准差, 清晰度)
    """
    # 转为 HWC 布局，确保内存连续
    img_hwc: torch.Tensor = torch.from_numpy(img).contiguous().to("cuda")

    # 包装为 CVCUDA Tensor，布局声明为 "HWC"
    img_tensor = cvcuda.as_tensor(img_hwc, layout="HWC")

    gray = cvcuda.cvtcolor(img_tensor, cvcuda.ColorConversion.BGR2GRAY)

    # 转 PyTorch GPU Tensor 并计算平均亮度
    gray_t = torch.as_tensor(gray.cuda()).squeeze(-1).float() * 100.0 / 255.0
    brightness = torch.mean(gray_t)  # 全局平均亮度

    # 对比度图：|pixel - brightness|
    contrast_map = torch.abs(gray_t - brightness)
    contrast_std = torch.std(contrast_map)

    # 清晰度评估：Laplacian + 方差
    lap = cvcuda.laplacian(gray, ksize=3, scale=1.0)
    lap_t = torch.as_tensor(lap.cuda()).float()
    sharpness = torch.var(lap_t)  # 方差代表高频成分强度

    return brightness, contrast_std, sharpness


def crop_square_center(image):
    """
    从图像中心裁剪正方形区域

    Args:
        image (numpy.ndarray): 输入图像

    Returns:
        tuple: (裁剪后的图像, x偏移, y偏移)
    """
    h, w = image.shape[:2]
    side = int(min(h, w))
    x_offset = (w - side) // 2
    y_offset = (h - side) // 2
    cropped_image = image[y_offset:y_offset + side, x_offset:x_offset + side]

    return cropped_image, x_offset, y_offset


def resize_image(image, target_size, keep_aspect_ratio=True):
    """
    调整图像尺寸

    Args:
        image (numpy.ndarray): 输入图像
        target_size (tuple): 目标尺寸 (width, height)
        keep_aspect_ratio (bool): 是否保持宽高比

    Returns:
        numpy.ndarray: 调整后的图像
    """
    if keep_aspect_ratio:
        h, w = image.shape[:2]
        target_w, target_h = target_size

        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        new_w = int(w * scale)
        new_h = int(h * scale)

        # 调整尺寸
        resized = cv2.resize(image, (new_w, new_h))

        # 创建目标尺寸的画布并居中放置
        canvas = np.zeros((target_h, target_w, 3), dtype=image.dtype)
        y_offset = (target_h - new_h) // 2
        x_offset = (target_w - new_w) // 2
        canvas[y_offset:y_offset + new_h, x_offset:x_offset + new_w] = resized

        return canvas
    else:
        return cv2.resize(image, target_size)


def enhance_image_quality(image, brightness_factor=1.0, contrast_factor=1.0, gamma=1.0):
    """
    增强图像质量

    Args:
        image (numpy.ndarray): 输入图像
        brightness_factor (float): 亮度因子
        contrast_factor (float): 对比度因子
        gamma (float): 伽马值

    Returns:
        numpy.ndarray: 增强后的图像
    """
    # 亮度调整
    if brightness_factor != 1.0:
        image = cv2.convertScaleAbs(image, alpha=1.0, beta=(brightness_factor - 1.0) * 50)

    # 对比度调整
    if contrast_factor != 1.0:
        image = cv2.convertScaleAbs(image, alpha=contrast_factor, beta=0)

    # 伽马校正
    if gamma != 1.0:
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
        image = cv2.LUT(image, table)

    return image


def calculate_image_metrics(image):
    """
    计算图像质量指标

    Args:
        image (numpy.ndarray): 输入图像

    Returns:
        dict: 图像质量指标
    """
    # 转换为灰度图
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image

    # 计算亮度
    brightness = np.mean(gray)

    # 计算对比度（标准差）
    contrast = np.std(gray)

    # 计算清晰度（Laplacian方差）
    laplacian = cv2.Laplacian(gray, cv2.CV_64F)
    sharpness = np.var(laplacian)

    # 计算噪声水平（高频成分）
    blur = cv2.GaussianBlur(gray, (5, 5), 0)
    noise = np.mean(np.abs(gray.astype(np.float32) - blur.astype(np.float32)))

    return {
        'brightness': brightness,
        'contrast': contrast,
        'sharpness': sharpness,
        'noise': noise
    }


def is_image_quality_acceptable(image, min_brightness=10, min_contrast=5, min_sharpness=100):
    """
    检查图像质量是否可接受

    Args:
        image (numpy.ndarray): 输入图像
        min_brightness (float): 最小亮度阈值
        min_contrast (float): 最小对比度阈值
        min_sharpness (float): 最小清晰度阈值

    Returns:
        bool: 图像质量是否可接受
    """
    try:
        brightness, contrast, sharpness = check_brightness_and_contrast(image)
        # 将tensor转换为Python标量进行比较
        return float(brightness) > min_brightness and float(contrast) > min_contrast and float(sharpness) > min_sharpness
    except Exception as e:
        print(f"图像质量检查错误: {e}")
        # 回退到简单的检查
        metrics = calculate_image_metrics(image)
        return (metrics['brightness'] > min_brightness and
                metrics['contrast'] > min_contrast and
                metrics['sharpness'] > min_sharpness)


def convert_color_space(image, conversion):
    """
    转换颜色空间

    Args:
        image (numpy.ndarray): 输入图像
        conversion: OpenCV颜色转换常数

    Returns:
        numpy.ndarray: 转换后的图像
    """
    return cv2.cvtColor(image, conversion)


def apply_histogram_equalization(image, method='clahe'):
    """
    应用直方图均衡化

    Args:
        image (numpy.ndarray): 输入图像
        method (str): 方法 ('standard', 'clahe')

    Returns:
        numpy.ndarray: 均衡化后的图像
    """
    if len(image.shape) == 3:
        # 彩色图像，转换到LAB空间并只对L通道进行均衡化
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        if method == 'clahe':
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
        else:
            l = cv2.equalizeHist(l)

        lab = cv2.merge([l, a, b])
        return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
    else:
        # 灰度图像
        if method == 'clahe':
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            return clahe.apply(image)
        else:
            return cv2.equalizeHist(image)
