#!/usr/bin/env python3
"""
图像处理工具模块
"""

import torch
import cvcuda


def check_brightness_and_contrast(img):
    """
    检查图像亮度和对比度

    Args:
        img (numpy.ndarray): 输入图像

    Returns:
        tuple: (亮度, 对比度标准差, 清晰度)
    """
    # 转为 HWC 布局，确保内存连续
    img_hwc: torch.Tensor = torch.from_numpy(img).contiguous().to("cuda")

    # 包装为 CVCUDA Tensor，布局声明为 "HWC"
    img_tensor = cvcuda.as_tensor(img_hwc, layout="HWC")

    gray = cvcuda.cvtcolor(img_tensor, cvcuda.ColorConversion.BGR2GRAY)

    # 转 PyTorch GPU Tensor 并计算平均亮度
    gray_t = torch.as_tensor(gray.cuda()).squeeze(-1).float() * 100.0 / 255.0
    brightness = torch.mean(gray_t)  # 全局平均亮度

    # 对比度图：|pixel - brightness|
    contrast_map = torch.abs(gray_t - brightness)
    contrast_std = torch.std(contrast_map)

    # 清晰度评估：Laplacian + 方差
    lap = cvcuda.laplacian(gray, ksize=3, scale=1.0)
    lap_t = torch.as_tensor(lap.cuda()).float()
    sharpness = torch.var(lap_t)  # 方差代表高频成分强度

    return brightness, contrast_std, sharpness


def crop_square_center(image):
    """
    从图像中心裁剪正方形区域

    Args:
        image (numpy.ndarray): 输入图像

    Returns:
        tuple: (裁剪后的图像, x偏移, y偏移)
    """
    h, w = image.shape[:2]
    side = int(min(h, w))
    x_offset = (w - side) // 2
    y_offset = (h - side) // 2
    cropped_image = image[y_offset:y_offset + side, x_offset:x_offset + side]

    return cropped_image, x_offset, y_offset