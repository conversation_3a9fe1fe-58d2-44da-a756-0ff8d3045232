#!/usr/bin/env python3
"""
可视化工具模块
"""

import cv2
import numpy as np
from math import cos, sin

# 可视化配置（硬编码）
VISUALIZATION_COLORS = {
    'bbox': (0, 255, 0),           # 绿色边界框
    'left_eye': (255, 0, 0),       # 蓝色左眼
    'right_eye': (255, 0, 0),      # 蓝色右眼
    'nose': (0, 255, 255),         # 黄色鼻子
    'left_mouth': (0, 0, 255),     # 红色左嘴角
    'right_mouth': (0, 0, 255),    # 红色右嘴角
    'quadrilateral': (0, 255, 255), # 青色四边形
    'border': (255, 255, 255),     # 白色边框
    'axis_x': (0, 0, 255),         # 红色X轴
    'axis_y': (0, 255, 0),         # 绿色Y轴
    'axis_z': (255, 0, 0),         # 蓝色Z轴
    'pose_text': (255, 255, 0),    # 黄色姿态文字
    'time_text': (0, 0, 255),      # 红色时间文字
    'score_text': (255, 255, 0),   # 黄色评分文字
    'deviation_text': (0, 255, 255), # 青色偏离度文字
}

# 字体配置（硬编码）
FONT_CONFIG = {
    'font': cv2.FONT_HERSHEY_SIMPLEX,
    'scale': 0.8,
    'thickness': 2,
    'large_scale': 2,
    'large_thickness': 3,
    'huge_scale': 3,
    'huge_thickness': 4
}


def draw_face_bbox(frame, bbox, confidence, face_id, color=None):
    """
    绘制人脸边界框

    Args:
        frame: 图像帧
        bbox: 边界框 [x1, y1, x2, y2]
        confidence: 置信度
        face_id: 人脸ID
        color: 颜色，默认使用配置中的颜色
    """
    color = color or VISUALIZATION_COLORS['bbox']
    x1, y1, x2, y2 = map(int, bbox)

    # 绘制边界框
    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 3)

    # 显示置信度和ID
    label = f"Face {face_id}: {confidence:.3f}"
    text_y = max(y1 - 10, 20)
    cv2.putText(
        frame, label, (x1, text_y),
        FONT_CONFIG['font'], 0.7, color, 2
    )


def draw_face_landmarks(frame, landmarks, x_offset=0, y_offset=0):
    """
    绘制人脸关键点

    Args:
        frame: 图像帧
        landmarks: 关键点
        x_offset: X坐标偏移
        y_offset: Y坐标偏移
    """
    if landmarks is None or len(landmarks) < 10:
        return

    # 解析关键点
    left_eye = (int(landmarks[0]) + x_offset, int(landmarks[1]) + y_offset)
    right_eye = (int(landmarks[2]) + x_offset, int(landmarks[3]) + y_offset)
    nose = (int(landmarks[4]) + x_offset, int(landmarks[5]) + y_offset)
    left_mouth = (int(landmarks[6]) + x_offset, int(landmarks[7]) + y_offset)
    right_mouth = (int(landmarks[8]) + x_offset, int(landmarks[9]) + y_offset)

    # 绘制关键点
    cv2.circle(frame, left_eye, 4, VISUALIZATION_COLORS['left_eye'], -1)
    cv2.circle(frame, right_eye, 4, VISUALIZATION_COLORS['right_eye'], -1)
    cv2.circle(frame, nose, 4, VISUALIZATION_COLORS['nose'], -1)
    cv2.circle(frame, left_mouth, 4, VISUALIZATION_COLORS['left_mouth'], -1)
    cv2.circle(frame, right_mouth, 4, VISUALIZATION_COLORS['right_mouth'], -1)

    # 添加白色边框
    cv2.circle(frame, left_eye, 5, VISUALIZATION_COLORS['border'], 1)
    cv2.circle(frame, right_eye, 5, VISUALIZATION_COLORS['border'], 1)
    cv2.circle(frame, nose, 5, VISUALIZATION_COLORS['border'], 1)
    cv2.circle(frame, left_mouth, 5, VISUALIZATION_COLORS['border'], 1)
    cv2.circle(frame, right_mouth, 5, VISUALIZATION_COLORS['border'], 1)


def draw_face_quadrilateral(frame, landmarks, x_offset=0, y_offset=0):
    """
    绘制人脸四边形

    Args:
        frame: 图像帧
        landmarks: 关键点
        x_offset: X坐标偏移
        y_offset: Y坐标偏移
    """
    if landmarks is None or len(landmarks) < 10:
        return

    # 解析关键点
    left_eye = (int(landmarks[0]) + x_offset, int(landmarks[1]) + y_offset)
    right_eye = (int(landmarks[2]) + x_offset, int(landmarks[3]) + y_offset)
    left_mouth = (int(landmarks[6]) + x_offset, int(landmarks[7]) + y_offset)
    right_mouth = (int(landmarks[8]) + x_offset, int(landmarks[9]) + y_offset)

    # 绘制四边形
    quad_points = np.array([left_eye, right_eye, right_mouth, left_mouth], np.int32)
    cv2.polylines(frame, [quad_points], True, VISUALIZATION_COLORS['quadrilateral'], 2)

    # 绘制关键点
    draw_face_landmarks(frame, landmarks, x_offset, y_offset)


def draw_geometry_validation_result(frame, is_valid_quad, nose_inside):
    """
    在图片右上角绘制几何验证结果

    Args:
        frame: 图像帧
        is_valid_quad: 四边形是否有效
        nose_inside: 鼻子是否在内部
    """
    _, w = frame.shape[:2]

    # 在右上角显示结果
    result_text_1 = f"Quad: {'T' if is_valid_quad else 'F'}"
    result_text_2 = f"Nose: {'T' if nose_inside else 'F'}"

    # 计算文本位置（右上角）
    font = FONT_CONFIG['font']
    font_scale = FONT_CONFIG['scale']
    thickness = FONT_CONFIG['thickness']

    # 获取文本尺寸
    (text_w1, text_h1), _ = cv2.getTextSize(result_text_1, font, font_scale, thickness)
    (text_w2, text_h2), _ = cv2.getTextSize(result_text_2, font, font_scale, thickness)

    # 计算位置
    margin = 20
    x1 = w - max(text_w1, text_w2) - margin
    y1 = margin + text_h1
    y2 = y1 + text_h2 + 10

    # 绘制背景矩形
    bg_x1 = x1 - 10
    bg_y1 = margin - 5
    bg_x2 = w - margin + 5
    bg_y2 = y2 + 10
    cv2.rectangle(frame, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)  # 黑色背景

    # 绘制文本
    color1 = (0, 255, 0) if is_valid_quad else (0, 0, 255)  # 绿色/红色
    color2 = (0, 255, 0) if nose_inside else (0, 0, 255)    # 绿色/红色

    cv2.putText(frame, result_text_1, (x1, y1), font, font_scale, color1, thickness)
    cv2.putText(frame, result_text_2, (x1, y2), font, font_scale, color2, thickness)


def draw_axis(img, yaw, pitch, roll, tdx=None, tdy=None, size=100):
    """
    绘制3D坐标轴

    Args:
        img: 图像
        yaw: 偏航角
        pitch: 俯仰角
        roll: 翻滚角
        tdx: 中心X坐标
        tdy: 中心Y坐标
        size: 轴长度
    """
    pitch = pitch * np.pi / 180
    yaw = -(yaw * np.pi / 180)
    roll = roll * np.pi / 180

    if tdx is not None and tdy is not None:
        tdx = tdx
        tdy = tdy
    else:
        height, width = img.shape[:2]
        tdx = width // 2
        tdy = height // 2

    # X轴指向右侧，绘制为红色
    x1 = size * (cos(yaw) * cos(roll)) + tdx
    y1 = size * (cos(pitch) * sin(roll) + cos(roll) * sin(pitch) * sin(yaw)) + tdy

    # Y轴绘制为绿色
    x2 = size * (-cos(yaw) * sin(roll)) + tdx
    y2 = size * (cos(pitch) * cos(roll) - sin(pitch) * sin(yaw) * sin(roll)) + tdy

    # Z轴（屏幕外）绘制为蓝色
    x3 = size * (sin(yaw)) + tdx
    y3 = size * (-cos(yaw) * sin(pitch)) + tdy

    cv2.line(img, (int(tdx), int(tdy)), (int(x1), int(y1)), VISUALIZATION_COLORS['axis_x'], 3)
    cv2.line(img, (int(tdx), int(tdy)), (int(x2), int(y2)), VISUALIZATION_COLORS['axis_y'], 3)
    cv2.line(img, (int(tdx), int(tdy)), (int(x3), int(y3)), VISUALIZATION_COLORS['axis_z'], 2)

    return img


def draw_pose_info(frame, pose_angles, bbox):
    """
    绘制姿态信息

    Args:
        frame: 图像帧
        pose_angles: 姿态角度字典
        bbox: 人脸边界框
    """
    if not pose_angles:
        return

    x1, y1, x2, y2 = map(int, bbox)

    # 绘制3D坐标轴
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2
    bbox_height = y2 - y1
    axis_size = bbox_height // 3

    draw_axis(frame, pose_angles['yaw'], pose_angles['pitch'], pose_angles['roll'],
             tdx=center_x, tdy=center_y, size=axis_size)

    # 显示姿态信息
    pose_text = f"Y:{pose_angles['yaw']:.1f} P:{pose_angles['pitch']:.1f} R:{pose_angles['roll']:.1f}"
    cv2.putText(frame, pose_text, (x1, y1 - 50),
               FONT_CONFIG['font'], 0.6, VISUALIZATION_COLORS['pose_text'], 2)


def draw_processing_info(frame, inference_time, attempt_count, geometry_score=None, deviation=None):
    """
    绘制处理信息

    Args:
        frame: 图像帧
        inference_time: 推理时间（毫秒）
        attempt_count: 尝试次数
        geometry_score: 几何评分
        deviation: 人脸偏离度
    """
    # 在左上角显示推理耗时和尝试次数
    time_label = f"{inference_time:.1f} ms (#{attempt_count})"
    cv2.putText(frame, time_label, (30, 70),
                FONT_CONFIG['font'], FONT_CONFIG['large_scale'],
                VISUALIZATION_COLORS['time_text'], FONT_CONFIG['large_thickness'])

    # 显示几何评分
    if geometry_score is not None:
        score_label = f"Geo Score: {geometry_score}/2"
        cv2.putText(frame, score_label, (30, 140),
                    FONT_CONFIG['font'], FONT_CONFIG['large_scale'],
                    VISUALIZATION_COLORS['score_text'], FONT_CONFIG['large_thickness'])

    # 显示人脸偏离度
    if deviation is not None:
        deviation_text = f"{deviation:.1f} deg"
        # 使用大字体和醒目颜色显示偏离度
        text_size = cv2.getTextSize(deviation_text, FONT_CONFIG['font'],
                                   FONT_CONFIG['huge_scale'], FONT_CONFIG['huge_thickness'])[0]
        cv2.rectangle(frame, (25, 200), (35 + text_size[0], 250), (0, 0, 0), -1)  # 黑色背景
        cv2.putText(frame, deviation_text, (30, 240),
                   FONT_CONFIG['font'], FONT_CONFIG['huge_scale'],
                   VISUALIZATION_COLORS['deviation_text'], FONT_CONFIG['huge_thickness'])


# ============================================================================
# 高级组合函数 (从 VisualizationHandler 合并而来)
# ============================================================================

def draw_face_detection_result(frame, face_info, x_offset: int, y_offset: int):
    """
    绘制人脸检测结果

    Args:
        frame: 图像帧
        face_info (dict): 人脸信息
        x_offset (int): X偏移
        y_offset (int): Y偏移

    Returns:
        tuple: 调整后的边界框
    """
    bbox = face_info['bbox']
    confidence = face_info['confidence']
    landmarks = face_info['landmarks']
    face_id = face_info['id']

    # 调整坐标到原图
    x1, y1, x2, y2 = map(int, bbox)
    x1, y1, x2, y2 = x1 + x_offset, y1 + y_offset, x2 + x_offset, y2 + y_offset
    adjusted_bbox = (x1, y1, x2, y2)

    # 绘制人脸边界框
    draw_face_bbox(frame, adjusted_bbox, confidence, face_id)

    # 绘制人脸四边形和关键点
    draw_face_quadrilateral(frame, landmarks, x_offset, y_offset)

    return adjusted_bbox


def draw_complete_result(frame, face_info, x_offset: int, y_offset: int,
                        is_valid_quad: bool, nose_inside: bool, inference_time: float,
                        attempt_count: int, geometry_score: int, pose_angles=None, deviation=None):
    """
    绘制完整的检测和验证结果

    Args:
        frame: 图像帧
        face_info (dict): 人脸信息
        x_offset (int): X偏移
        y_offset (int): Y偏移
        is_valid_quad (bool): 四边形是否有效
        nose_inside (bool): 鼻子是否在内部
        inference_time (float): 推理时间
        attempt_count (int): 尝试次数
        geometry_score (int): 几何评分
        pose_angles (dict): 姿态角度字典
        deviation (float): 人脸偏离度

    Returns:
        tuple: 调整后的边界框
    """
    # 绘制人脸检测结果
    adjusted_bbox = draw_face_detection_result(frame, face_info, x_offset, y_offset)

    # 绘制几何验证结果
    draw_geometry_validation_result(frame, is_valid_quad, nose_inside)

    # 绘制处理信息
    draw_processing_info(frame, inference_time, attempt_count, geometry_score, deviation)

    # 绘制姿态信息（如果有）
    if pose_angles is not None:
        draw_pose_info(frame, pose_angles, adjusted_bbox)

    return adjusted_bbox


# ============================================================================
# 工具函数
# ============================================================================

def print_visualization_legend(enable_hopenet=False):
    """
    打印可视化说明

    Args:
        enable_hopenet (bool): 是否启用HopeNet
    """
    print("\n" + "=" * 80)
    if enable_hopenet:
        print("RetinaFace + HopeNet 人脸几何形状验证与朝向分析可视化说明:")
    else:
        print("RetinaFace 人脸几何形状验证可视化说明:")
    print("=" * 80)
    print("- 绿色矩形框: 人脸边界框")
    print("- 蓝色圆点: 眼睛位置")
    print("- 黄色圆点: 鼻子位置")
    print("- 红色圆点: 嘴角位置")
    print("- 白色边框: 关键点轮廓")
    print("- 黄色线条: 左眼-右眼-右嘴角-左嘴角构成的四边形")
    print("- 右上角 Quad T/F: 四边形是否有效（无自相交）")
    print("- 右上角 Nose T/F: 鼻子是否在四边形内部")
    print("- 左上角推理时间和尝试次数")
    print("- Geo Score: 几何评分 (2/2为完全通过)")
    print("- Face ID + 置信度: 人脸编号和检测置信度")

    if enable_hopenet:
        print("- 红色线条: X轴 (左右方向)")
        print("- 绿色线条: Y轴 (上下方向)")
        print("- 蓝色线条: Z轴 (前后方向)")
        print("- 黄色文字: 姿态角度 (Yaw/Pitch/Roll)")
        print("- 左上角大号黄色数字: 人脸偏离度 (度数，越小越正对屏幕)")
    else:
        print("- HopeNet人脸朝向检测功能已禁用")
        print("- 如需启用朝向分析，请使用 --enable-hopenet 参数")

    print("=" * 80)
