#!/usr/bin/env python3
"""
人脸分析器包
"""

from .core.geometry_validator import FaceGeometryValidator
from .core.hopenet_estimator import HopeNetEstimator
from .batch_processor import BatchProcessor

__version__ = "1.0.0"
__author__ = "Face Analyzer Team"
__description__ = "基于RetinaFace的人脸几何形状验证与朝向分析工具"

__all__ = [
    'RetinaFaceDetector',
    'FaceGeometryValidator', 
    'HopeNetEstimator',
    'BatchProcessor'
]
