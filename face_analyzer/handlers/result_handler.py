#!/usr/bin/env python3
"""
结果处理器模块
"""

import os
import cv2


class ResultHandler:
    """结果处理器类"""

    def __init__(self):
        """初始化结果处理器"""
        pass

    def _ensure_directory_exists(self, file_path: str):
        """
        确保文件所在目录存在

        Args:
            file_path (str): 文件路径
        """
        save_dir = os.path.dirname(file_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir, exist_ok=True)

    def save_result(self, frame, save_path: str, attempt_count: int,
                   is_final: bool = False, timestamp: str = None, geometry_score: int = None):
        """
        保存结果（最终结果或中间结果）

        Args:
            frame: 图像帧
            save_path (str): 基础保存路径
            attempt_count (int): 尝试次数
            is_final (bool): 是否为最终结果
            timestamp (str): 时间戳（仅最终结果需要）
            geometry_score (int): 几何评分（仅中间结果需要）

        Returns:
            bool: 保存是否成功
        """
        try:
            if is_final:
                # 最终结果：使用原始路径
                actual_save_path = save_path
                success_msg = f"✓ 找到有效几何形状! 时间戳: {timestamp}, 尝试次数: {attempt_count}"
                error_msg = "❌ 保存最终结果失败"
            else:
                # 中间结果：添加序号后缀
                base_path, ext = os.path.splitext(save_path)
                actual_save_path = f"{base_path}_{attempt_count}{ext}"
                success_msg = f"⚠️ 几何验证未通过 (评分: {geometry_score}/2)，保存中间结果: {actual_save_path}"
                error_msg = "❌ 保存中间结果失败"

            # 确保保存目录存在
            self._ensure_directory_exists(actual_save_path)

            # 保存图像
            cv2.imwrite(actual_save_path, frame)
            print(success_msg)
            return True

        except Exception as e:
            print(f"{error_msg}: {e}")
            return False

    def save_final_result(self, frame, save_path: str, timestamp: str, attempt_count: int):
        """
        保存最终成功结果

        Args:
            frame: 图像帧
            save_path (str): 保存路径
            timestamp (str): 时间戳
            attempt_count (int): 尝试次数

        Returns:
            bool: 保存是否成功
        """
        return self.save_result(frame, save_path, attempt_count,
                               is_final=True, timestamp=timestamp)

    def save_intermediate_result(self, frame, save_path: str, attempt_count: int, geometry_score: int):
        """
        保存中间结果

        Args:
            frame: 图像帧
            save_path (str): 基础保存路径
            attempt_count (int): 尝试次数
            geometry_score (int): 几何评分

        Returns:
            bool: 保存是否成功
        """
        return self.save_result(frame, save_path, attempt_count,
                               is_final=False, geometry_score=geometry_score)

    def is_geometry_valid(self, geometry_score: int) -> bool:
        """
        检查几何形状是否有效

        Args:
            geometry_score (int): 几何评分

        Returns:
            bool: 几何形状是否有效（评分为2）
        """
        return geometry_score == 2

    def should_save_intermediate(self, faces, confidence_threshold: float = 0.75) -> bool:
        """
        判断是否应该保存中间结果

        Args:
            faces (list): 人脸列表
            confidence_threshold (float): 置信度阈值

        Returns:
            bool: 是否应该保存中间结果
        """
        if not faces:
            return False

        max_confidence = max([face['confidence'] for face in faces])
        return max_confidence > confidence_threshold
