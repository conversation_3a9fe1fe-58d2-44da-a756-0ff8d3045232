#!/usr/bin/env python3
"""
结果处理器模块
"""

import os
import cv2


class ResultHandler:
    """结果处理器类"""

    def __init__(self):
        """初始化结果处理器"""
        pass

    def save_final_result(self, frame, save_path: str, timestamp: str, attempt_count: int):
        """
        保存最终成功结果
        
        Args:
            frame: 图像帧
            save_path (str): 保存路径
            timestamp (str): 时间戳
            attempt_count (int): 尝试次数
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保保存目录存在
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)

            cv2.imwrite(save_path, frame)
            print(f"✓ 找到有效几何形状! 时间戳: {timestamp}, 尝试次数: {attempt_count}")
            return True
        except Exception as e:
            print(f"❌ 保存最终结果失败: {e}")
            return False

    def save_intermediate_result(self, frame, save_path: str, attempt_count: int, geometry_score: int):
        """
        保存中间结果
        
        Args:
            frame: 图像帧
            save_path (str): 基础保存路径
            attempt_count (int): 尝试次数
            geometry_score (int): 几何评分
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 生成带序号的中间结果路径
            base_path, ext = os.path.splitext(save_path)
            intermediate_path = f"{base_path}_{attempt_count}{ext}"
            
            # 确保保存目录存在
            save_dir = os.path.dirname(intermediate_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)

            cv2.imwrite(intermediate_path, frame)
            print(f"⚠️ 几何验证未通过 (评分: {geometry_score}/2)，保存中间结果: {intermediate_path}")
            return True
        except Exception as e:
            print(f"❌ 保存中间结果失败: {e}")
            return False

    def is_geometry_valid(self, geometry_score: int) -> bool:
        """
        检查几何形状是否有效
        
        Args:
            geometry_score (int): 几何评分
            
        Returns:
            bool: 几何形状是否有效（评分为2）
        """
        return geometry_score == 2

    def should_save_intermediate(self, faces, confidence_threshold: float = 0.75) -> bool:
        """
        判断是否应该保存中间结果
        
        Args:
            faces (list): 人脸列表
            confidence_threshold (float): 置信度阈值
            
        Returns:
            bool: 是否应该保存中间结果
        """
        if not faces:
            return False
        
        max_confidence = max([face['confidence'] for face in faces])
        return max_confidence > confidence_threshold
