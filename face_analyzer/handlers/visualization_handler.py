#!/usr/bin/env python3
"""
可视化处理器模块
"""

from ..utils.visualization import (
    draw_face_bbox, draw_face_quadrilateral, draw_geometry_validation_result,
    draw_pose_info, draw_processing_info
)


class VisualizationHandler:
    """可视化处理器类"""

    def __init__(self):
        """初始化可视化处理器"""
        pass

    def draw_face_detection_result(self, frame, face_info, x_offset: int, y_offset: int):
        """
        绘制人脸检测结果
        
        Args:
            frame: 图像帧
            face_info (dict): 人脸信息
            x_offset (int): X偏移
            y_offset (int): Y偏移
        """
        bbox = face_info['bbox']
        confidence = face_info['confidence']
        landmarks = face_info['landmarks']
        face_id = face_info['id']
        
        # 调整坐标到原图
        x1, y1, x2, y2 = map(int, bbox)
        x1, y1, x2, y2 = x1 + x_offset, y1 + y_offset, x2 + x_offset, y2 + y_offset
        adjusted_bbox = (x1, y1, x2, y2)
        
        # 绘制人脸边界框
        draw_face_bbox(frame, adjusted_bbox, confidence, face_id)
        
        # 绘制人脸四边形和关键点
        draw_face_quadrilateral(frame, landmarks, x_offset, y_offset)
        
        return adjusted_bbox

    def draw_geometry_result(self, frame, is_valid_quad: bool, nose_inside: bool):
        """
        绘制几何验证结果
        
        Args:
            frame: 图像帧
            is_valid_quad (bool): 四边形是否有效
            nose_inside (bool): 鼻子是否在内部
        """
        draw_geometry_validation_result(frame, is_valid_quad, nose_inside)

    def draw_pose_result(self, frame, pose_angles, bbox):
        """
        绘制姿态估计结果
        
        Args:
            frame: 图像帧
            pose_angles (dict): 姿态角度字典
            bbox: 人脸边界框
        """
        if pose_angles is not None:
            draw_pose_info(frame, pose_angles, bbox)

    def draw_processing_result(self, frame, inference_time: float, attempt_count: int, 
                             geometry_score: int = None, deviation: float = None):
        """
        绘制处理信息
        
        Args:
            frame: 图像帧
            inference_time (float): 推理时间（毫秒）
            attempt_count (int): 尝试次数
            geometry_score (int): 几何评分
            deviation (float): 人脸偏离度
        """
        draw_processing_info(frame, inference_time, attempt_count, geometry_score, deviation)

    def draw_complete_result(self, frame, face_info, x_offset: int, y_offset: int,
                           is_valid_quad: bool, nose_inside: bool, inference_time: float,
                           attempt_count: int, geometry_score: int, pose_angles=None, deviation=None):
        """
        绘制完整的检测和验证结果
        
        Args:
            frame: 图像帧
            face_info (dict): 人脸信息
            x_offset (int): X偏移
            y_offset (int): Y偏移
            is_valid_quad (bool): 四边形是否有效
            nose_inside (bool): 鼻子是否在内部
            inference_time (float): 推理时间
            attempt_count (int): 尝试次数
            geometry_score (int): 几何评分
            pose_angles (dict): 姿态角度字典
            deviation (float): 人脸偏离度
            
        Returns:
            tuple: 调整后的边界框
        """
        # 绘制人脸检测结果
        adjusted_bbox = self.draw_face_detection_result(frame, face_info, x_offset, y_offset)
        
        # 绘制几何验证结果
        self.draw_geometry_result(frame, is_valid_quad, nose_inside)
        
        # 绘制处理信息
        self.draw_processing_result(frame, inference_time, attempt_count, geometry_score, deviation)
        
        # 绘制姿态信息（如果有）
        if pose_angles is not None:
            self.draw_pose_result(frame, pose_angles, adjusted_bbox)
        
        return adjusted_bbox
