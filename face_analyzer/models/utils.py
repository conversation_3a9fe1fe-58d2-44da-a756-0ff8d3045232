# Copyright (c) Alibaba, Inc. and its affiliates.
# Utilities extracted from ModelScope to enable decoupling

import json
import os.path as osp
from pathlib import Path

import cv2
import numpy as np

import PIL.Image
from PIL import Image, ImageOps


def load_image(image_path_or_url, mode='RGB', backend='pillow'):
    """Load image from path or URL

    Args:
        image_path_or_url: Path to image file or URL
        mode: Color mode ('RGB', 'BGR', etc.)
        backend: Backend to use ('pillow' or 'cv2')

    Returns:
        PIL Image object
    """
    if backend == 'cv2':
        img = cv2.imread(image_path_or_url, cv2.IMREAD_COLOR)
        if mode == 'RGB':
            cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)
        img = Image.fromarray(img)
    elif backend == 'pillow':
        with open(image_path_or_url, 'rb') as f:
            img = Image.open(f)
            img = ImageOps.exif_transpose(img)
            img = img.convert(mode)
    else:
        raise ValueError(f"Unsupported backend: {backend}")

    return img


class LoadImage:
    """Image loading utility class"""

    def __init__(self, mode='RGB', backend='pillow'):
        self.mode = mode
        self.backend = backend

    @staticmethod
    def convert_to_ndarray(input):
        """Convert various input types to numpy array

        Args:
            input: Input image (str path, PIL Image, numpy array, or dict)

        Returns:
            numpy array representation of the image
        """
        if isinstance(input, str):
            img = np.array(load_image(input))
        elif isinstance(input, PIL.Image.Image):
            img = np.array(input.convert('RGB'))
        elif isinstance(input, np.ndarray):
            if len(input.shape) == 2:
                input = cv2.cvtColor(input, cv2.COLOR_GRAY2BGR)
            img = input[:, :, ::-1]  # BGR to RGB
        elif isinstance(input, dict):
            img_data = input.get('image', None)
            if img_data:
                img = np.array(load_image(img_data))
            else:
                raise ValueError("Dict input must contain 'image' key")
        else:
            raise TypeError(f'input should be either str, PIL.Image, '
                          f'np.array, or dict, but got {type(input)}')
        return img


class ConfigDict(dict):
    """Dictionary that supports attribute-style access"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for key, value in self.items():
            if isinstance(value, dict):
                self[key] = ConfigDict(value)

    def __getattr__(self, key):
        try:
            return self[key]
        except KeyError:
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{key}'")

    def __setattr__(self, key, value):
        self[key] = value

    def __delattr__(self, key):
        try:
            del self[key]
        except KeyError:
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{key}'")


class Config:
    """Configuration class that supports loading from files"""

    def __init__(self, cfg_dict=None, cfg_text=None, filename=None):
        if cfg_dict is None:
            cfg_dict = dict()
        elif not isinstance(cfg_dict, dict):
            raise TypeError('cfg_dict must be a dict, but '
                          f'got {type(cfg_dict)}')

        if isinstance(filename, Path):
            filename = str(filename)

        self._cfg_dict = ConfigDict(cfg_dict)
        self._filename = filename
        if cfg_text:
            text = cfg_text
        elif filename:
            with open(filename, 'r', encoding='utf-8') as f:
                text = f.read()
        else:
            text = ''
        self._text = text

    @property
    def filename(self):
        return self._filename

    @property
    def text(self):
        return self._text

    def __getattr__(self, name):
        return getattr(self._cfg_dict, name)

    def __getitem__(self, name):
        return self._cfg_dict[name]

    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            self._cfg_dict[name] = value

    def __setitem__(self, name, value):
        self._cfg_dict[name] = value

    def __iter__(self):
        return iter(self._cfg_dict)

    def __len__(self):
        return len(self._cfg_dict)

    def keys(self):
        return self._cfg_dict.keys()

    def values(self):
        return self._cfg_dict.values()

    def items(self):
        return self._cfg_dict.items()

    @staticmethod
    def _file2dict(filename):
        """Load config from file"""
        filename = osp.abspath(osp.expanduser(filename))
        if not osp.isfile(filename):
            raise FileNotFoundError(f'File {filename} not found')

        if filename.endswith('.py'):
            # Load Python config file
            import importlib.util
            spec = importlib.util.spec_from_file_location("config", filename)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            cfg_dict = {name: value for name, value in config_module.__dict__.items()
                       if not name.startswith('__')}
        elif filename.endswith(('.yml', '.yaml')):
            # Load YAML config file
            import yaml
            with open(filename, 'r', encoding='utf-8') as f:
                cfg_dict = yaml.safe_load(f)
        elif filename.endswith('.json'):
            # Load JSON config file
            with open(filename, 'r', encoding='utf-8') as f:
                cfg_dict = json.load(f)
        else:
            raise ValueError(f'Unsupported config file format: {filename}')

        cfg_text = filename + '\n'
        with open(filename, 'r', encoding='utf-8') as f:
            cfg_text += f.read()

        return cfg_dict, cfg_text

    @staticmethod
    def from_file(filename):
        """Create Config from file"""
        if isinstance(filename, Path):
            filename = str(filename)
        cfg_dict, cfg_text = Config._file2dict(filename)
        return Config(cfg_dict, cfg_text=cfg_text, filename=filename)


def is_on_same_device(model):
    """Check if all model parameters are on the same device"""
    device_set = set(str(p.device) for p in model.parameters()) - {'cpu'}
    return len(device_set) <= 1
