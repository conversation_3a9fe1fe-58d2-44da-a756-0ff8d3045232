#!/usr/bin/env python3
"""
Single image head pose estimation using Hopenet with YOLOv11 face detection.
This script combines YOLOv11 face detection from Hugging Face with Hopenet head pose estimation.

Features:
- Automatic face detection using YOLOv11n-face-detection from AdamCodd/YOLOv11n-face-detection
- Head pose estimation using Hopenet
- Dockerface-style bbox expansion and processing
- Support for multiple faces per image
- Configurable confidence thresholds
"""

import sys, os, argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt

import torch
import torch.nn as nn
from torch.autograd import Variable
from torchvision import transforms
import torch.backends.cudnn as cudnn
import torchvision
import torch.nn.functional as F
from PIL import Image

import datasets, hopenet, utils

# YOLOv11 and Hugging Face imports
try:
    from ultralytics import YOLO
    from huggingface_hub import hf_hub_download
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("Warning: YOLOv11 dependencies not available. Please install: pip install ultralytics huggingface_hub")

def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description='Head pose estimation with YOLOv11 face detection and Hopenet.')
    parser.add_argument('--gpu', dest='gpu_id', help='GPU device id to use [0]',
            default=0, type=int)
    parser.add_argument('--snapshot', dest='snapshot', help='Path of Hopenet model snapshot.',
          default='/home/<USER>/video_summerization/webp_generator/deep-head-pose/hopenet_robust_alpha1.pkl', type=str)
    parser.add_argument('--image', dest='image_path', help='Path of input image', required=True)
    parser.add_argument('--output_dir', dest='output_dir', help='Output directory', default='output/images')
    parser.add_argument('--output_name', dest='output_name', help='Output filename (without extension)', default='yolo_result')

    # YOLO face detection parameters
    parser.add_argument('--yolo_conf_threshold', dest='yolo_conf_threshold',
                       help='YOLO face detection confidence threshold', type=float, default=0.5)


    # Pose estimation parameters
    parser.add_argument('--pose_conf_threshold', dest='pose_conf_threshold',
                       help='Confidence threshold for pose estimation', type=float, default=0.5)
    parser.add_argument('--bbox_expansion', dest='bbox_expansion', help='Bbox expansion in pixels',
                       type=int, default=50)

    # Fallback options
    parser.add_argument('--manual_bbox', nargs='+', type=str,
                       help='Manual bounding boxes: "x_min,y_min,x_max,y_max,conf" (fallback if YOLO fails)')
    parser.add_argument('--no_yolo', action='store_true', help='Skip YOLO detection and use manual bboxes only')

    args = parser.parse_args()
    return args

def load_yolo_model(device='cpu'):
    """Load YOLOv11 face detection model from Hugging Face"""
    try:
        print("Loading YOLOv11n-face-detection from Hugging Face...")

        # Download model using huggingface_hub
        repo_id = "AdamCodd/YOLOv11n-face-detection"
        filename = "model.pt"

        print("Downloading model file...")
        model_path = hf_hub_download(repo_id=repo_id, filename=filename)
        print(f"Model downloaded to: {model_path}")

        # Load the model
        model = YOLO(model_path)
        print("Model loaded successfully!")

        # Move model to device
        model.to(device)
        return model

    except Exception as e:
        print(f"Error loading YOLO model: {e}")
        print("Please check your internet connection and ensure huggingface_hub is installed:")
        print("pip install huggingface_hub")
        return None

def detect_faces_yolo(image, model, conf_threshold=0.5):
    """Detect faces using YOLOv11"""
    try:
        # Run inference
        results = model(image, conf=conf_threshold, verbose=False)

        faces = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Get coordinates and confidence
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf = float(box.conf[0].cpu().numpy())

                    # Convert to integers
                    x_min, y_min, x_max, y_max = int(x1), int(y1), int(x2), int(y2)

                    faces.append((x_min, y_min, x_max, y_max, conf))

        return faces

    except Exception as e:
        print(f"YOLO face detection failed: {e}")
        return []

def parse_manual_bboxes(manual_bbox_list):
    """Parse manual bounding boxes from command line"""
    bboxes = []
    if not manual_bbox_list:
        return bboxes

    for bbox_str in manual_bbox_list:
        try:
            parts = bbox_str.split(',')
            if len(parts) >= 4:
                x_min = int(float(parts[0]))
                y_min = int(float(parts[1]))
                x_max = int(float(parts[2]))
                y_max = int(float(parts[3]))
                conf = float(parts[4]) if len(parts) > 4 else 1.0
                bboxes.append((x_min, y_min, x_max, y_max, conf))
            else:
                print(f"Warning: Invalid manual bbox format: {bbox_str}")
        except ValueError as e:
            print(f"Warning: Error parsing manual bbox {bbox_str}: {e}")

    return bboxes

def expand_bbox_dockerface_style(x_min, y_min, x_max, y_max, img_width, img_height, expansion=50):
    """Expand bounding box using dockerface-style expansion"""
    # Apply fixed pixel expansion (as in original script)
    x_min -= expansion
    x_max += expansion
    y_min -= expansion
    y_max += int(expansion * 0.6)  # Less expansion on bottom

    # Ensure coordinates are within image bounds
    x_min = max(x_min, 0)
    y_min = max(y_min, 0)
    x_max = min(img_width, x_max)
    y_max = min(img_height, y_max)

    return x_min, y_min, x_max, y_max

def process_image_with_yolo_and_pose(image_path, yolo_model, hopenet_model, transformations, idx_tensor, gpu, args):
    """Process image with YOLO face detection and Hopenet pose estimation"""

    # Load image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")

    # Load image with PIL first to handle various formats
    try:
        pil_image = Image.open(image_path)
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        # Convert PIL to OpenCV format
        image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"Error loading image with PIL: {e}")
        # Fallback to OpenCV
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")

    original_image = image.copy()
    img_height, img_width = image.shape[:2]

    print(f"Image loaded: {img_width}x{img_height}")

    # Get bounding boxes
    bboxes = []

    if not args.no_yolo and yolo_model is not None:
        # Use YOLO for face detection
        print("Running YOLO face detection...")
        yolo_faces = detect_faces_yolo(image, yolo_model, args.yolo_conf_threshold)
        print(f"YOLO detected {len(yolo_faces)} faces")
        bboxes.extend(yolo_faces)

    # Add manual bboxes if provided
    if args.manual_bbox:
        manual_faces = parse_manual_bboxes(args.manual_bbox)
        print(f"Adding {len(manual_faces)} manual bounding boxes")
        bboxes.extend(manual_faces)

    if not bboxes:
        print("No faces detected. Try lowering --yolo_conf_threshold or provide --manual_bbox")
        return original_image, []

    print(f"Processing {len(bboxes)} total bounding boxes")

    results = []
    processed_faces = 0

    # Process each bounding box
    for i, (x_min, y_min, x_max, y_max, conf) in enumerate(bboxes):
        print(f"Processing bbox {i+1}/{len(bboxes)} (confidence: {conf:.3f})")

        # Apply confidence threshold for pose estimation
        if conf < args.pose_conf_threshold:
            print(f"  Skipping bbox {i+1} due to low confidence ({conf:.3f} < {args.pose_conf_threshold})")
            continue

        # Expand bounding box using dockerface style
        expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max = expand_bbox_dockerface_style(
            x_min, y_min, x_max, y_max, img_width, img_height, args.bbox_expansion)

        print(f"  Original bbox: ({x_min}, {y_min}, {x_max}, {y_max})")
        print(f"  Expanded bbox: ({expanded_x_min}, {expanded_y_min}, {expanded_x_max}, {expanded_y_max})")

        # Crop face region
        face_img = image[expanded_y_min:expanded_y_max, expanded_x_min:expanded_x_max]

        if face_img.size == 0:
            print(f"  Invalid face crop for bbox {i+1}, skipping...")
            continue

        # Convert to RGB for PIL
        face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
        face_pil = Image.fromarray(face_rgb)

        # Apply transformations for Hopenet
        try:
            transformed_img = transformations(face_pil)
            img_shape = transformed_img.size()
            transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])

            # Move to GPU if available
            if torch.cuda.is_available():
                transformed_img = Variable(transformed_img).cuda(gpu)
            else:
                transformed_img = Variable(transformed_img)

            # Forward pass through Hopenet
            with torch.no_grad():
                yaw, pitch, roll = hopenet_model(transformed_img)

                # Apply softmax and get predictions
                yaw_predicted = F.softmax(yaw, dim=1)
                pitch_predicted = F.softmax(pitch, dim=1)
                roll_predicted = F.softmax(roll, dim=1)

                # Get continuous predictions in degrees
                yaw_predicted = torch.sum(yaw_predicted.data[0] * idx_tensor) * 3 - 99
                pitch_predicted = torch.sum(pitch_predicted.data[0] * idx_tensor) * 3 - 99
                roll_predicted = torch.sum(roll_predicted.data[0] * idx_tensor) * 3 - 99

                # Convert to float for easier handling
                yaw_deg = float(yaw_predicted)
                pitch_deg = float(pitch_predicted)
                roll_deg = float(roll_predicted)

                print(f"  Face {processed_faces+1} - Yaw: {yaw_deg:.2f}°, Pitch: {pitch_deg:.2f}°, Roll: {roll_deg:.2f}°")

                # Draw pose visualization on original image
                center_x = (expanded_x_min + expanded_x_max) // 2
                center_y = (expanded_y_min + expanded_y_max) // 2
                bbox_height = expanded_y_max - expanded_y_min
                size = bbox_height // 2

                utils.draw_axis(original_image, yaw_deg, pitch_deg, roll_deg,
                              tdx=center_x, tdy=center_y, size=size)

                # Draw YOLO detection box (green)
                cv2.rectangle(original_image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)

                # Draw expanded bounding box (blue)
                cv2.rectangle(original_image, (expanded_x_min, expanded_y_min),
                            (expanded_x_max, expanded_y_max), (255, 0, 0), 1)

                # Add text with pose information
                text = f"F{processed_faces+1}: Y:{yaw_deg:.1f} P:{pitch_deg:.1f} R:{roll_deg:.1f}"
                cv2.putText(original_image, text, (x_min, y_min-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                # Add confidence text
                conf_text = f"Conf: {conf:.2f}"
                cv2.putText(original_image, conf_text, (x_min, y_max+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

                results.append({
                    'face_id': processed_faces + 1,
                    'yolo_bbox': (x_min, y_min, x_max, y_max),
                    'expanded_bbox': (expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max),
                    'yolo_confidence': conf,
                    'yaw': yaw_deg,
                    'pitch': pitch_deg,
                    'roll': roll_deg
                })

                processed_faces += 1

        except Exception as e:
            print(f"  Error processing bbox {i+1}: {e}")
            continue

    print(f"Successfully processed {processed_faces} faces")
    return original_image, results

def main():
    """Main function"""
    args = parse_args()

    # Check YOLO availability
    if not YOLO_AVAILABLE and not args.no_yolo:
        print("Error: YOLO dependencies not available. Install with:")
        print("pip install ultralytics huggingface_hub")
        print("Or use --no_yolo with --manual_bbox")
        sys.exit(1)

    # Check CUDA availability
    if torch.cuda.is_available():
        print(f"CUDA available. Using GPU {args.gpu_id}")
        cudnn.enabled = True
        device = f'cuda:{args.gpu_id}'
    else:
        print("CUDA not available. Using CPU")
        device = 'cpu'
        args.gpu_id = 'cpu'

    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)

    # Load YOLO model
    yolo_model = None
    if not args.no_yolo and YOLO_AVAILABLE:
        yolo_model = load_yolo_model(device)
        if yolo_model is None:
            print("Warning: Failed to load YOLO model. Will use manual bboxes only.")

    # Load Hopenet model
    print("Loading Hopenet model...")
    hopenet_model = hopenet.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)

    # Load snapshot if provided
    if args.snapshot and os.path.exists(args.snapshot):
        print(f"Loading Hopenet snapshot: {args.snapshot}")
        saved_state_dict = torch.load(args.snapshot, map_location=device)
        hopenet_model.load_state_dict(saved_state_dict)
    else:
        print("Warning: No Hopenet snapshot provided. Using random weights.")

    # Move Hopenet model to device
    if torch.cuda.is_available():
        hopenet_model.cuda(args.gpu_id)
    hopenet_model.eval()

    # Setup transformations for Hopenet
    transformations = transforms.Compose([
        transforms.Resize(224),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Setup index tensor for pose calculation
    idx_tensor = [idx for idx in range(66)]
    if torch.cuda.is_available():
        idx_tensor = torch.FloatTensor(idx_tensor).cuda(args.gpu_id)
    else:
        idx_tensor = torch.FloatTensor(idx_tensor)

    print("Ready to process image.")

    try:
        # Process the image
        result_image, results = process_image_with_yolo_and_pose(
            args.image_path, yolo_model, hopenet_model, transformations, idx_tensor, args.gpu_id, args)

        # Save result image
        output_path = os.path.join(args.output_dir, f"{args.output_name}.jpg")
        cv2.imwrite(output_path, result_image)
        print(f"Result image saved to: {output_path}")

        # Save results to text file
        txt_output_path = os.path.join(args.output_dir, f"{args.output_name}.txt")
        with open(txt_output_path, 'w') as f:
            f.write(f"Head Pose Estimation Results (YOLO + Hopenet) for: {args.image_path}\n")
            f.write("="*75 + "\n")
            f.write(f"YOLO confidence threshold: {args.yolo_conf_threshold}\n")
            f.write(f"Pose confidence threshold: {args.pose_conf_threshold}\n")
            f.write(f"Bbox expansion: {args.bbox_expansion} pixels\n")
            f.write(f"YOLO model used: {'Yes' if yolo_model else 'No'}\n")
            f.write("="*75 + "\n")
            for result in results:
                f.write(f"Face {result['face_id']}:\n")
                f.write(f"  YOLO Bounding Box: {result['yolo_bbox']}\n")
                f.write(f"  Expanded Bounding Box: {result['expanded_bbox']}\n")
                f.write(f"  YOLO Confidence: {result['yolo_confidence']:.3f}\n")
                f.write(f"  Yaw: {result['yaw']:.2f}°\n")
                f.write(f"  Pitch: {result['pitch']:.2f}°\n")
                f.write(f"  Roll: {result['roll']:.2f}°\n")
                f.write("\n")

        print(f"Results saved to: {txt_output_path}")
        print(f"Processed {len(results)} faces successfully.")

        if len(results) == 0:
            print("\nTips for better results:")
            print("- Try lowering --yolo_conf_threshold (default: 0.5)")
            print("- Try lowering --pose_conf_threshold (default: 0.5)")
            print("- Provide manual bboxes with --manual_bbox")
            print("- Check if the image contains clear, frontal faces")

    except Exception as e:
        print(f"Error processing image: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
