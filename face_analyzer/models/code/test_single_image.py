#!/usr/bin/env python3
"""
Single image head pose estimation using the Hopenet network.
Based on the original test_on_video_dlib.py but adapted for single image processing.

This script performs head pose estimation on a single image file.
Supports various image formats including .jpg, .png, .webp, .gif, etc.
"""

import sys, os, argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt

import torch
import torch.nn as nn
from torch.autograd import Variable
from torchvision import transforms
import torch.backends.cudnn as cudnn
import torchvision
import torch.nn.functional as F
from PIL import Image

import datasets, hopenet, utils

try:
    import dlib
    DLIB_AVAILABLE = True
except ImportError:
    DLIB_AVAILABLE = False
    print("Warning: dlib not available. Face detection will be skipped.")

def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description='Head pose estimation on single image using Hopenet.')
    parser.add_argument('--gpu', dest='gpu_id', help='GPU device id to use [0]',
            default=0, type=int)
    parser.add_argument('--snapshot', dest='snapshot', help='Path of model snapshot.',
          default='', type=str)
    parser.add_argument('--face_model', dest='face_model', help='Path of DLIB face detection model.',
          default='', type=str)
    parser.add_argument('--image', dest='image_path', help='Path of input image', required=True)
    parser.add_argument('--output_dir', dest='output_dir', help='Output directory', default='output/images')
    parser.add_argument('--output_name', dest='output_name', help='Output filename (without extension)', default='result')
    parser.add_argument('--no_face_detection', action='store_true', help='Skip face detection and use whole image')
    parser.add_argument('--bbox', nargs=4, type=int, metavar=('x_min', 'y_min', 'x_max', 'y_max'),
                       help='Manual bounding box coordinates (x_min y_min x_max y_max)')
    args = parser.parse_args()
    return args

def detect_faces_dlib(image, face_detector):
    """Detect faces using dlib CNN face detector"""
    if not DLIB_AVAILABLE:
        return []
    
    try:
        # Convert BGR to RGB for dlib
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        detections = face_detector(rgb_image, 1)
        
        faces = []
        for det in detections:
            x_min = det.rect.left()
            y_min = det.rect.top()
            x_max = det.rect.right()
            y_max = det.rect.bottom()
            conf = det.confidence
            faces.append((x_min, y_min, x_max, y_max, conf))
        
        return faces
    except Exception as e:
        print(f"Face detection failed: {e}")
        return []

def expand_bbox(x_min, y_min, x_max, y_max, img_width, img_height, expansion_factor=0.3):
    """Expand bounding box for better head pose estimation"""
    bbox_width = abs(x_max - x_min)
    bbox_height = abs(y_max - y_min)
    
    # Expand the bounding box
    x_min -= expansion_factor * bbox_width
    x_max += expansion_factor * bbox_width
    y_min -= expansion_factor * bbox_height
    y_max += expansion_factor * bbox_height
    
    # Ensure coordinates are within image bounds
    x_min = max(0, int(x_min))
    y_min = max(0, int(y_min))
    x_max = min(img_width, int(x_max))
    y_max = min(img_height, int(y_max))
    
    return x_min, y_min, x_max, y_max

def process_image(image_path, model, transformations, idx_tensor, gpu, args):
    """Process a single image for head pose estimation"""
    
    # Load image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")
    
    # Load image with PIL first to handle various formats including webp
    try:
        pil_image = Image.open(image_path)
        # Convert to RGB if necessary
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        # Convert PIL to OpenCV format
        image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"Error loading image with PIL: {e}")
        # Fallback to OpenCV
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
    
    original_image = image.copy()
    img_height, img_width = image.shape[:2]
    
    print(f"Image loaded: {img_width}x{img_height}")
    
    # Face detection or manual bbox
    faces = []
    
    if args.bbox:
        # Use manual bounding box
        x_min, y_min, x_max, y_max = args.bbox
        faces = [(x_min, y_min, x_max, y_max, 1.0)]
        print(f"Using manual bounding box: ({x_min}, {y_min}, {x_max}, {y_max})")
    
    elif not args.no_face_detection and args.face_model and DLIB_AVAILABLE:
        # Use dlib face detection
        try:
            face_detector = dlib.cnn_face_detection_model_v1(args.face_model)
            faces = detect_faces_dlib(image, face_detector)
            print(f"Detected {len(faces)} faces")
        except Exception as e:
            print(f"Face detection failed: {e}")
            faces = []
    
    # If no faces detected or face detection disabled, use whole image
    if not faces:
        print("No faces detected or face detection disabled. Using whole image.")
        faces = [(0, 0, img_width, img_height, 1.0)]
    
    results = []
    
    # Process each detected face
    for i, (x_min, y_min, x_max, y_max, conf) in enumerate(faces):
        print(f"Processing face {i+1}/{len(faces)} (confidence: {conf:.2f})")
        
        # Expand bounding box for better context
        x_min, y_min, x_max, y_max = expand_bbox(x_min, y_min, x_max, y_max, img_width, img_height)
        
        # Crop face region
        face_img = image[y_min:y_max, x_min:x_max]
        
        if face_img.size == 0:
            print(f"Invalid face crop for face {i+1}, skipping...")
            continue
        
        # Convert to RGB for PIL
        face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
        face_pil = Image.fromarray(face_rgb)
        
        # Apply transformations
        try:
            transformed_img = transformations(face_pil)
            img_shape = transformed_img.size()
            transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])
            
            # Move to GPU if available
            if torch.cuda.is_available():
                transformed_img = Variable(transformed_img).cuda(gpu)
            else:
                transformed_img = Variable(transformed_img)
            
            # Forward pass
            with torch.no_grad():
                yaw, pitch, roll = model(transformed_img)
                
                # Apply softmax and get predictions
                yaw_predicted = F.softmax(yaw, dim=1)
                pitch_predicted = F.softmax(pitch, dim=1)
                roll_predicted = F.softmax(roll, dim=1)
                
                # Get continuous predictions in degrees
                yaw_predicted = torch.sum(yaw_predicted.data[0] * idx_tensor) * 3 - 99
                pitch_predicted = torch.sum(pitch_predicted.data[0] * idx_tensor) * 3 - 99
                roll_predicted = torch.sum(roll_predicted.data[0] * idx_tensor) * 3 - 99
                
                # Convert to float for easier handling
                yaw_deg = float(yaw_predicted)
                pitch_deg = float(pitch_predicted)
                roll_deg = float(roll_predicted)
                
                print(f"Face {i+1} - Yaw: {yaw_deg:.2f}°, Pitch: {pitch_deg:.2f}°, Roll: {roll_deg:.2f}°")
                
                # Draw pose visualization on original image
                center_x = (x_min + x_max) // 2
                center_y = (y_min + y_max) // 2
                size = max(x_max - x_min, y_max - y_min) // 3
                
                utils.draw_axis(original_image, yaw_deg, pitch_deg, roll_deg, 
                              tdx=center_x, tdy=center_y, size=size)
                
                # Draw bounding box
                cv2.rectangle(original_image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
                
                # Add text with pose information
                text = f"Y:{yaw_deg:.1f} P:{pitch_deg:.1f} R:{roll_deg:.1f}"
                cv2.putText(original_image, text, (x_min, y_min-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                results.append({
                    'face_id': i+1,
                    'bbox': (x_min, y_min, x_max, y_max),
                    'confidence': conf,
                    'yaw': yaw_deg,
                    'pitch': pitch_deg,
                    'roll': roll_deg
                })
                
        except Exception as e:
            print(f"Error processing face {i+1}: {e}")
            continue
    
    return original_image, results

def main():
    """Main function"""
    args = parse_args()
    
    # Check CUDA availability
    if torch.cuda.is_available():
        print(f"CUDA available. Using GPU {args.gpu_id}")
        cudnn.enabled = True
        device = f'cuda:{args.gpu_id}'
    else:
        print("CUDA not available. Using CPU")
        device = 'cpu'
        args.gpu_id = 'cpu'
    
    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Load model
    print("Loading Hopenet model...")
    model = hopenet.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)
    
    # Load snapshot if provided
    if args.snapshot and os.path.exists(args.snapshot):
        print(f"Loading snapshot: {args.snapshot}")
        saved_state_dict = torch.load(args.snapshot, map_location=device)
        model.load_state_dict(saved_state_dict)
    else:
        print("Warning: No snapshot provided or file not found. Using random weights.")
    
    # Move model to device
    if torch.cuda.is_available():
        model.cuda(args.gpu_id)
    model.eval()
    
    # Setup transformations
    transformations = transforms.Compose([
        transforms.Resize(224),
        transforms.CenterCrop(224), 
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Setup index tensor for pose calculation
    idx_tensor = [idx for idx in range(66)]
    if torch.cuda.is_available():
        idx_tensor = torch.FloatTensor(idx_tensor).cuda(args.gpu_id)
    else:
        idx_tensor = torch.FloatTensor(idx_tensor)
    
    print("Ready to process image.")
    
    try:
        # Process the image
        result_image, results = process_image(args.image_path, model, transformations, 
                                            idx_tensor, args.gpu_id, args)
        
        # Save result image
        output_path = os.path.join(args.output_dir, f"{args.output_name}.jpg")
        cv2.imwrite(output_path, result_image)
        print(f"Result image saved to: {output_path}")
        
        # Save results to text file
        txt_output_path = os.path.join(args.output_dir, f"{args.output_name}.txt")
        with open(txt_output_path, 'w') as f:
            f.write(f"Head Pose Estimation Results for: {args.image_path}\n")
            f.write("="*50 + "\n")
            for result in results:
                f.write(f"Face {result['face_id']}:\n")
                f.write(f"  Bounding Box: {result['bbox']}\n")
                f.write(f"  Confidence: {result['confidence']:.3f}\n")
                f.write(f"  Yaw: {result['yaw']:.2f}°\n")
                f.write(f"  Pitch: {result['pitch']:.2f}°\n")
                f.write(f"  Roll: {result['roll']:.2f}°\n")
                f.write("\n")
        
        print(f"Results saved to: {txt_output_path}")
        print(f"Processed {len(results)} faces successfully.")
        
    except Exception as e:
        print(f"Error processing image: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
