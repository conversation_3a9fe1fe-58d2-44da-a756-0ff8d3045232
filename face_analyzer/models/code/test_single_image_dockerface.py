#!/usr/bin/env python3
"""
Single image head pose estimation using the Hopenet network with dockerface-style bounding boxes.
Based on the original test_on_video_dockerface.py but adapted for single image processing.

This script processes a single image with pre-computed face bounding boxes in dockerface format.
It supports multiple faces per image and applies the same confidence filtering and bbox expansion
as the original video processing script.
"""

import sys, os, argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt

import torch
import torch.nn as nn
from torch.autograd import Variable
from torchvision import transforms
import torch.backends.cudnn as cudnn
import torchvision
import torch.nn.functional as F
from PIL import Image

import datasets, hopenet, utils

def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description='Head pose estimation on single image using Hopenet with dockerface-style bboxes.')
    parser.add_argument('--gpu', dest='gpu_id', help='GPU device id to use [0]',
            default=0, type=int)
    parser.add_argument('--snapshot', dest='snapshot', help='Path of model snapshot.',
          default='', type=str)
    parser.add_argument('--image', dest='image_path', help='Path of input image', required=True)
    parser.add_argument('--bboxes', dest='bboxes', help='Path to bounding box annotations file')
    parser.add_argument('--output_dir', dest='output_dir', help='Output directory', default='output/images')
    parser.add_argument('--output_name', dest='output_name', help='Output filename (without extension)', default='dockerface_result')
    parser.add_argument('--confidence_threshold', dest='conf_threshold', help='Confidence threshold for face detection', 
                       type=float, default=0.98)
    parser.add_argument('--bbox_expansion', dest='bbox_expansion', help='Bbox expansion in pixels', 
                       type=int, default=50)
    parser.add_argument('--manual_bbox', nargs='+', type=str, 
                       help='Manual bounding boxes: "x_min,y_min,x_max,y_max,conf" (can specify multiple)')
    args = parser.parse_args()
    return args

def parse_bbox_line(line):
    """Parse a single bounding box line"""
    parts = line.strip().split()
    if len(parts) >= 5:
        # Format: frame_id x_min y_min x_max y_max conf [additional_data...]
        frame_id = int(parts[0])
        x_min = int(float(parts[1]))
        y_min = int(float(parts[2]))
        x_max = int(float(parts[3]))
        y_max = int(float(parts[4]))
        conf = float(parts[5]) if len(parts) > 5 else 1.0
        return frame_id, x_min, y_min, x_max, y_max, conf
    else:
        raise ValueError(f"Invalid bbox format: {line}")

def load_bboxes_from_file(bbox_file):
    """Load bounding boxes from dockerface-style annotation file"""
    bboxes = []
    try:
        with open(bbox_file, 'r') as f:
            lines = f.read().splitlines()
        
        for line in lines:
            if line.strip():  # Skip empty lines
                try:
                    frame_id, x_min, y_min, x_max, y_max, conf = parse_bbox_line(line)
                    bboxes.append((x_min, y_min, x_max, y_max, conf))
                except ValueError as e:
                    print(f"Warning: Skipping invalid bbox line: {line} ({e})")
                    continue
        
        print(f"Loaded {len(bboxes)} bounding boxes from {bbox_file}")
        return bboxes
        
    except Exception as e:
        print(f"Error loading bboxes from {bbox_file}: {e}")
        return []

def parse_manual_bboxes(manual_bbox_list):
    """Parse manual bounding boxes from command line"""
    bboxes = []
    for bbox_str in manual_bbox_list:
        try:
            parts = bbox_str.split(',')
            if len(parts) >= 4:
                x_min = int(float(parts[0]))
                y_min = int(float(parts[1]))
                x_max = int(float(parts[2]))
                y_max = int(float(parts[3]))
                conf = float(parts[4]) if len(parts) > 4 else 1.0
                bboxes.append((x_min, y_min, x_max, y_max, conf))
            else:
                print(f"Warning: Invalid manual bbox format: {bbox_str}")
        except ValueError as e:
            print(f"Warning: Error parsing manual bbox {bbox_str}: {e}")
    
    return bboxes

def expand_bbox_dockerface_style(x_min, y_min, x_max, y_max, img_width, img_height, expansion=50):
    """Expand bounding box using dockerface-style expansion"""
    # Apply fixed pixel expansion (as in original script)
    x_min -= expansion
    x_max += expansion
    y_min -= expansion
    y_max += int(expansion * 0.6)  # Less expansion on bottom
    
    # Ensure coordinates are within image bounds
    x_min = max(x_min, 0)
    y_min = max(y_min, 0)
    x_max = min(img_width, x_max)
    y_max = min(img_height, y_max)
    
    return x_min, y_min, x_max, y_max

def process_image_with_bboxes(image_path, bboxes, model, transformations, idx_tensor, gpu, args):
    """Process a single image with given bounding boxes"""
    
    # Load image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")
    
    # Load image with PIL first to handle various formats
    try:
        pil_image = Image.open(image_path)
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        # Convert PIL to OpenCV format
        image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"Error loading image with PIL: {e}")
        # Fallback to OpenCV
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
    
    original_image = image.copy()
    img_height, img_width = image.shape[:2]
    
    print(f"Image loaded: {img_width}x{img_height}")
    print(f"Processing {len(bboxes)} bounding boxes")
    
    results = []
    processed_faces = 0
    
    # Process each bounding box
    for i, (x_min, y_min, x_max, y_max, conf) in enumerate(bboxes):
        print(f"Processing bbox {i+1}/{len(bboxes)} (confidence: {conf:.3f})")
        
        # Apply confidence threshold (as in original script)
        if conf <= args.conf_threshold:
            print(f"  Skipping bbox {i+1} due to low confidence ({conf:.3f} <= {args.conf_threshold})")
            continue
        
        # Expand bounding box using dockerface style
        expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max = expand_bbox_dockerface_style(
            x_min, y_min, x_max, y_max, img_width, img_height, args.bbox_expansion)
        
        print(f"  Original bbox: ({x_min}, {y_min}, {x_max}, {y_max})")
        print(f"  Expanded bbox: ({expanded_x_min}, {expanded_y_min}, {expanded_x_max}, {expanded_y_max})")
        
        # Crop face region
        face_img = image[expanded_y_min:expanded_y_max, expanded_x_min:expanded_x_max]
        
        if face_img.size == 0:
            print(f"  Invalid face crop for bbox {i+1}, skipping...")
            continue
        
        # Convert to RGB for PIL
        face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
        face_pil = Image.fromarray(face_rgb)
        
        # Apply transformations
        try:
            transformed_img = transformations(face_pil)
            img_shape = transformed_img.size()
            transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])
            
            # Move to GPU if available
            if torch.cuda.is_available():
                transformed_img = Variable(transformed_img).cuda(gpu)
            else:
                transformed_img = Variable(transformed_img)
            
            # Forward pass
            with torch.no_grad():
                yaw, pitch, roll = model(transformed_img)
                
                # Apply softmax and get predictions (as in original script)
                yaw_predicted = F.softmax(yaw, dim=1)
                pitch_predicted = F.softmax(pitch, dim=1)
                roll_predicted = F.softmax(roll, dim=1)
                
                # Get continuous predictions in degrees
                yaw_predicted = torch.sum(yaw_predicted.data[0] * idx_tensor) * 3 - 99
                pitch_predicted = torch.sum(pitch_predicted.data[0] * idx_tensor) * 3 - 99
                roll_predicted = torch.sum(roll_predicted.data[0] * idx_tensor) * 3 - 99
                
                # Convert to float for easier handling
                yaw_deg = float(yaw_predicted)
                pitch_deg = float(pitch_predicted)
                roll_deg = float(roll_predicted)
                
                print(f"  Face {processed_faces+1} - Yaw: {yaw_deg:.2f}°, Pitch: {pitch_deg:.2f}°, Roll: {roll_deg:.2f}°")
                
                # Draw pose visualization on original image (using expanded bbox center)
                center_x = (expanded_x_min + expanded_x_max) // 2
                center_y = (expanded_y_min + expanded_y_max) // 2
                bbox_height = expanded_y_max - expanded_y_min
                size = bbox_height // 2
                
                utils.draw_axis(original_image, yaw_deg, pitch_deg, roll_deg, 
                              tdx=center_x, tdy=center_y, size=size)
                
                # Draw original bounding box (before expansion)
                cv2.rectangle(original_image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
                
                # Draw expanded bounding box
                cv2.rectangle(original_image, (expanded_x_min, expanded_y_min), 
                            (expanded_x_max, expanded_y_max), (255, 0, 0), 1)
                
                # Add text with pose information
                text = f"F{processed_faces+1}: Y:{yaw_deg:.1f} P:{pitch_deg:.1f} R:{roll_deg:.1f}"
                cv2.putText(original_image, text, (x_min, y_min-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                results.append({
                    'face_id': processed_faces + 1,
                    'original_bbox': (x_min, y_min, x_max, y_max),
                    'expanded_bbox': (expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max),
                    'confidence': conf,
                    'yaw': yaw_deg,
                    'pitch': pitch_deg,
                    'roll': roll_deg
                })
                
                processed_faces += 1
                
        except Exception as e:
            print(f"  Error processing bbox {i+1}: {e}")
            continue
    
    print(f"Successfully processed {processed_faces} faces")
    return original_image, results

def main():
    """Main function"""
    args = parse_args()
    
    # Check CUDA availability
    if torch.cuda.is_available():
        print(f"CUDA available. Using GPU {args.gpu_id}")
        cudnn.enabled = True
        device = f'cuda:{args.gpu_id}'
    else:
        print("CUDA not available. Using CPU")
        device = 'cpu'
        args.gpu_id = 'cpu'
    
    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Load model
    print("Loading Hopenet model...")
    model = hopenet.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)
    
    # Load snapshot if provided
    if args.snapshot and os.path.exists(args.snapshot):
        print(f"Loading snapshot: {args.snapshot}")
        saved_state_dict = torch.load(args.snapshot, map_location=device)
        model.load_state_dict(saved_state_dict)
    else:
        print("Warning: No snapshot provided or file not found. Using random weights.")
    
    # Move model to device
    if torch.cuda.is_available():
        model.cuda(args.gpu_id)
    model.eval()
    
    # Setup transformations
    transformations = transforms.Compose([
        transforms.Resize(224),
        transforms.CenterCrop(224), 
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Setup index tensor for pose calculation
    idx_tensor = [idx for idx in range(66)]
    if torch.cuda.is_available():
        idx_tensor = torch.FloatTensor(idx_tensor).cuda(args.gpu_id)
    else:
        idx_tensor = torch.FloatTensor(idx_tensor)
    
    # Load bounding boxes
    bboxes = []
    
    if args.manual_bbox:
        # Use manual bounding boxes
        bboxes = parse_manual_bboxes(args.manual_bbox)
        print(f"Using {len(bboxes)} manual bounding boxes")
    elif args.bboxes and os.path.exists(args.bboxes):
        # Load from file
        bboxes = load_bboxes_from_file(args.bboxes)
    else:
        print("Error: No bounding boxes provided. Use --bboxes file or --manual_bbox")
        sys.exit(1)
    
    if not bboxes:
        print("Error: No valid bounding boxes found")
        sys.exit(1)
    
    print("Ready to process image.")
    
    try:
        # Process the image
        result_image, results = process_image_with_bboxes(args.image_path, bboxes, model, 
                                                        transformations, idx_tensor, args.gpu_id, args)
        
        # Save result image
        output_path = os.path.join(args.output_dir, f"{args.output_name}.jpg")
        cv2.imwrite(output_path, result_image)
        print(f"Result image saved to: {output_path}")
        
        # Save results to text file
        txt_output_path = os.path.join(args.output_dir, f"{args.output_name}.txt")
        with open(txt_output_path, 'w') as f:
            f.write(f"Head Pose Estimation Results (Dockerface-style) for: {args.image_path}\n")
            f.write("="*70 + "\n")
            f.write(f"Confidence threshold: {args.conf_threshold}\n")
            f.write(f"Bbox expansion: {args.bbox_expansion} pixels\n")
            f.write("="*70 + "\n")
            for result in results:
                f.write(f"Face {result['face_id']}:\n")
                f.write(f"  Original Bounding Box: {result['original_bbox']}\n")
                f.write(f"  Expanded Bounding Box: {result['expanded_bbox']}\n")
                f.write(f"  Confidence: {result['confidence']:.3f}\n")
                f.write(f"  Yaw: {result['yaw']:.2f}°\n")
                f.write(f"  Pitch: {result['pitch']:.2f}°\n")
                f.write(f"  Roll: {result['roll']:.2f}°\n")
                f.write("\n")
        
        print(f"Results saved to: {txt_output_path}")
        print(f"Processed {len(results)} faces successfully.")
        
    except Exception as e:
        print(f"Error processing image: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
