#!/usr/bin/env python3
"""
人脸分析器全局配置
"""

import torch

# ============================================================================
# 全局配置
# ============================================================================

# 人脸检测相关配置
SCORE_THRESHOLD = 0.98
MIN_FACE_SIZE = 30  # 最小人脸尺寸（像素）
MIN_FACE_RATIO = 0.1  # 最小人脸占画面比例

# HopeNet相关配置
ENABLE_HOPENET = True  # 默认禁用HopeNet功能，可通过命令行参数启用
HOPENET_WEIGHTS_PATH = '/home/<USER>/video_summerization/test_script/face_analyzer/models/hopenet/hopenet_robust_alpha1.pkl'
DEEP_HEAD_POSE_PATH = '/home/<USER>/video_summerization/test_script/face_analyzer/models/code'

# 几何验证相关配置
GEOMETRY_TOLERANCE = 1e-6      # 几何计算容差

# 视频处理相关配置
SAMPLE_INTERVAL_SEC = 3  # 采样间隔（秒）
MIN_BRIGHTNESS = 10      # 最小亮度阈值
MIN_CONTRAST = 5         # 最小对比度阈值

# 设备配置
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

# 模型路径配置
RETINAFACE_MODEL_PATH = 'models/cv_resnet50_face-detection_retinaface'

# 支持的视频格式
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)


