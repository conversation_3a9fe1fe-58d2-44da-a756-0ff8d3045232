#!/usr/bin/env python3
"""
人脸分析器全局配置
"""

import torch

# ============================================================================
# 全局配置
# ============================================================================

# 人脸检测相关配置
SCORE_THRESHOLD = 0.98
MIN_FACE_SIZE = 30  # 最小人脸尺寸（像素）
MIN_FACE_RATIO = 0.1  # 最小人脸占画面比例

# HopeNet相关配置
ENABLE_HOPENET = True  # 默认禁用HopeNet功能，可通过命令行参数启用
HOPENET_WEIGHTS_PATH = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/hopenet_robust_alpha1.pkl'
DEEP_HEAD_POSE_PATH = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/code'

# 几何验证相关配置
GEOMETRY_CHECK_ENABLED = True  # 启用几何形状验证
GEOMETRY_TOLERANCE = 1e-6      # 几何计算容差

# 视频处理相关配置
SAMPLE_INTERVAL_SEC = 3  # 采样间隔（秒）
MIN_BRIGHTNESS = 10      # 最小亮度阈值
MIN_CONTRAST = 5         # 最小对比度阈值

# 设备配置
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

# 模型路径配置
RETINAFACE_MODEL_PATH = 'models/cv_resnet50_face-detection_retinaface'

# 支持的视频格式
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

# 可视化配置
VISUALIZATION_COLORS = {
    'bbox': (0, 255, 0),           # 绿色边界框
    'left_eye': (255, 0, 0),       # 蓝色左眼
    'right_eye': (255, 0, 0),      # 蓝色右眼
    'nose': (0, 255, 255),         # 黄色鼻子
    'left_mouth': (0, 0, 255),     # 红色左嘴角
    'right_mouth': (0, 0, 255),    # 红色右嘴角
    'quadrilateral': (255, 255, 0), # 黄色四边形
    'border': (255, 255, 255),     # 白色边框
    'axis_x': (0, 0, 255),         # 红色X轴
    'axis_y': (0, 255, 0),         # 绿色Y轴
    'axis_z': (255, 0, 0),         # 蓝色Z轴
    'pose_text': (255, 255, 0),    # 黄色姿态文字
    'time_text': (0, 0, 255),      # 红色时间文字
    'score_text': (255, 255, 0),   # 黄色评分文字
    'deviation_text': (0, 255, 255), # 黄色偏离度文字
}

# 字体配置
FONT_CONFIG = {
    'font': 0,  # cv2.FONT_HERSHEY_SIMPLEX
    'scale': 1.5,
    'thickness': 3,
    'large_scale': 2.0,
    'large_thickness': 3,
    'huge_scale': 3.0,
    'huge_thickness': 4,
}
