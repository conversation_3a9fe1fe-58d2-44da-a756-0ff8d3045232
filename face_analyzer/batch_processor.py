#!/usr/bin/env python3
"""
批处理器模块
"""

import os
import cv2
import time
import logging
from tqdm import tqdm

from .core.face_detector import create_retinaface_detector
from .core.geometry_validator import FaceGeometryValidator
from .core.hopenet_estimator import HopeNetEstimator
from .utils.image_utils import check_brightness_and_contrast, crop_square_center
from .utils.visualization import (
    draw_face_bbox, draw_face_quadrilateral, draw_geometry_validation_result,
    draw_pose_info, draw_processing_info
)
from .config.settings import (
    VIDEO_EXTENSIONS, SAMPLE_INTERVAL_SEC,
    MIN_BRIGHTNESS, MIN_CONTRAST, MIN_FACE_SIZE, MIN_FACE_RATIO
)


class BatchProcessor:
    """批处理器类"""

    def __init__(self, enable_hopenet=False):
        """
        初始化批处理器

        Args:
            enable_hopenet (bool): 是否启用HopeNet
        """
        self.detector = create_retinaface_detector()
        self.geometry_validator = FaceGeometryValidator()
        self.hopenet_estimator = HopeNetEstimator(enable=enable_hopenet)

        print("✓ 批处理器初始化完成")

    def find_first_valid_geometry_face(self, video_path: str, save_image_path: str) -> str:
        """
        每隔指定秒数抽帧，用 RetinaFace 检测人脸并验证几何形状。
        找到第一个通过几何验证的人脸时：
          1. 保存该帧到 save_image_path；
          2. 返回 HH:MM:SS 时间戳。
        若视频末尾仍未找到，返回 "00:00:00" 且不保存。

        在找到有效几何形状之前，会保存中间结果图片（文件名带_1, _2等后缀）

        Args:
            video_path (str): 视频文件路径
            save_image_path (str): 检测到有效几何形状时，保存帧图像的路径

        Returns:
            str: FFmpeg 支持的时间戳字符串 "HH:MM:SS"
        """
        if not os.path.isfile(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 确保保存目录存在
        save_dir = os.path.dirname(save_image_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir, exist_ok=True)

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        # 获取视频基本参数
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        sample_step = max(1, int(fps * SAMPLE_INTERVAL_SEC))

        global_idx = 0
        attempt_count = 0  # 尝试次数计数器

        print("开始几何形状验证")

        while True:
            ret, frame = cap.read()

            if not ret:
                break  # 视频结束

            if global_idx % sample_step == 0:
                # 检查图像质量，修复tensor比较问题
                brightness, contrast, sharpness = check_brightness_and_contrast(frame)
                if float(brightness) > MIN_BRIGHTNESS and float(contrast) > MIN_CONTRAST:
                    start_time = time.time()

                    h, w = frame.shape[:2]
                    # 计算正方形裁剪区域
                    cropped_frame, x_offset, y_offset = crop_square_center(frame)

                    # 使用RetinaFace进行推理
                    # 将numpy数组转换为模型输入格式
                    input_data = {'img': cropped_frame}
                    result = self.detector(input_data)

                    # 初始化过滤后的人脸列表
                    filtered_faces = []

                    # 直接解析RetinaFace检测结果
                    if result is not None and len(result) == 2:
                        dets, landms = result

                        if dets is not None and len(dets) > 0:
                            # 过滤人脸
                            for i in range(len(dets)):
                                det = dets[i]
                                if len(det) < 5:
                                    continue

                                x1, y1, x2, y2, confidence = det[:5]

                                if confidence < 0.75:
                                    continue

                                # 过滤掉长、宽小于30像素的检测框
                                width = x2 - x1
                                height = y2 - y1
                                if width <= MIN_FACE_SIZE or height <= MIN_FACE_SIZE:
                                    continue

                                # 过滤掉长、宽小于画面10%的检测框
                                if width <= w*MIN_FACE_RATIO or height <= h*MIN_FACE_RATIO:
                                    continue

                                # 获取对应的关键点
                                landmarks = None
                                if landms is not None and i < len(landms):
                                    landmarks = landms[i]

                                if landmarks is not None:
                                    face_info = {
                                        'id': i + 1,
                                        'confidence': confidence,
                                        'bbox': [x1, y1, x2, y2],
                                        'landmarks': landmarks
                                    }
                                    filtered_faces.append(face_info)

                    if filtered_faces and max([face['confidence'] for face in filtered_faces]) > 0.75:
                        attempt_count += 1
                        inference_time = (time.time() - start_time) * 1000

                        # 处理每个检测到的人脸
                        best_face = None
                        best_geometry_score = -1

                        for face in filtered_faces:
                            landmarks = face['landmarks']
                            if landmarks:
                                # 进行几何形状验证
                                geometry_score = self.geometry_validator.calculate_geometry_score(landmarks)

                                if geometry_score > best_geometry_score:
                                    best_geometry_score = geometry_score
                                    best_face = {
                                        'face': face,
                                        'geometry_score': geometry_score
                                    }

                        if best_face:
                            face = best_face['face']
                            geometry_score = best_face['geometry_score']
                            is_valid_quad, nose_inside = self.geometry_validator.validate_face_geometry(face['landmarks'])

                            # 绘制检测结果
                            bbox = face['bbox']
                            confidence = face['confidence']
                            landmarks = face['landmarks']
                            face_id = face['id']
                            x1, y1, x2, y2 = map(int, bbox)

                            # 调整坐标到原图
                            x1, y1, x2, y2 = x1+x_offset, y1+y_offset, x2+x_offset, y2+y_offset
                            adjusted_bbox = (x1, y1, x2, y2)

                            # 估计头部姿态（如果启用HopeNet）
                            pose_angles = None
                            deviation = None
                            if self.hopenet_estimator.available:
                                pose_angles = self.hopenet_estimator.estimate_head_pose(frame, adjusted_bbox)
                                if pose_angles:
                                    deviation = self.hopenet_estimator.calculate_face_deviation(pose_angles)

                            # 绘制可视化结果
                            draw_face_bbox(frame, adjusted_bbox, confidence, face_id)
                            draw_face_quadrilateral(frame, landmarks, x_offset, y_offset)
                            draw_geometry_validation_result(frame, is_valid_quad, nose_inside)
                            draw_processing_info(frame, inference_time, attempt_count, geometry_score, deviation)

                            if pose_angles:
                                draw_pose_info(frame, pose_angles, adjusted_bbox)

                            # 决定是否保存图片
                            if geometry_score == 2:  # 两个条件都满足
                                # 保存最终成功的帧
                                cv2.imwrite(save_image_path, frame)
                                timestamp = time.strftime("%H:%M:%S", time.gmtime(global_idx / fps))
                                print(f"✓ 找到有效几何形状! 时间戳: {timestamp}, 尝试次数: {attempt_count}")
                                cap.release()
                                return timestamp
                            else:
                                # 保存中间结果（带序号后缀）
                                base_path, ext = os.path.splitext(save_image_path)
                                intermediate_path = f"{base_path}_{attempt_count}{ext}"
                                cv2.imwrite(intermediate_path, frame)
                                print(f"⚠️ 几何验证未通过 (评分: {geometry_score}/2)，保存中间结果: {intermediate_path}")

            global_idx += 1

            if not ret or global_idx >= total_frames:
                break  # 全部读取完毕

        cap.release()
        print(f"❌ 视频结束，未找到有效几何形状。总尝试次数: {attempt_count}")
        return "00:00:00"

    def process_videos(self, input_base: str, output_base: str):
        """
        批量处理视频文件

        Args:
            input_base (str): 输入基础路径
            output_base (str): 输出基础路径
        """
        # 收集所有视频文件
        video_paths = []

        if os.path.isfile(input_base):
            # 如果输入是单个文件
            if input_base.lower().endswith(VIDEO_EXTENSIONS):
                video_paths.append(input_base)
        else:
            # 如果输入是目录
            for root, _, files in os.walk(input_base):
                for fname in files:
                    if fname.lower().endswith(VIDEO_EXTENSIONS):
                        video_paths.append(os.path.join(root, fname))

        # 确保基础输出目录存在
        os.makedirs(output_base, exist_ok=True)

        # 已处理文件记录路径
        processed_file = os.path.join(output_base, 'processed_geometry.log')
        # 加载已处理的文件
        processed_set = set()
        if os.path.exists(processed_file):
            with open(processed_file, 'r') as pf:
                processed_set = set(line.strip() for line in pf if line.strip())

        # 过滤掉已处理的视频
        pending_paths = [p for p in video_paths if p not in processed_set]

        # 设置独立的日志记录器
        success_log = os.path.join(output_base, 'success_geometry.log')
        error_log = os.path.join(output_base, 'error_geometry.log')

        # 错误日志记录器
        error_logger = logging.getLogger('geometry_error')
        error_logger.setLevel(logging.ERROR)
        err_handler = logging.FileHandler(error_log)
        err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        error_logger.addHandler(err_handler)

        # 成功日志记录器
        success_logger = logging.getLogger('geometry_success')
        success_logger.setLevel(logging.INFO)
        succ_handler = logging.FileHandler(success_log)
        succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        success_logger.addHandler(succ_handler)

        # 使用进度条处理
        for input_path in tqdm(pending_paths, desc="Processing videos with geometry validation", unit="file"):
            # 根据相对路径和文件名确定输出目录
            fname = os.path.basename(input_path)
            base_name, _ = os.path.splitext(fname)

            if os.path.isfile(input_base):
                # 如果输入是单个文件，直接保存到输出目录
                output_dir = output_base
                output_jpg = os.path.join(output_dir, base_name+".jpg")
            else:
                # 如果输入是目录，保持目录结构
                root = os.path.dirname(input_path)
                rel_dir = os.path.relpath(root, input_base)
                output_dir = os.path.join(output_base, rel_dir)
                output_jpg = os.path.join(output_dir, base_name+".jpg")

            os.makedirs(output_dir, exist_ok=True)

            # try:
            if True:
                ts = self.find_first_valid_geometry_face(input_path, output_jpg)
                # 标记为已处理
                with open(processed_file, 'a') as pf:
                    pf.write(f"{input_path}\n")
            # except Exception as e:
            #     error_logger.error(
            #         f"Failed processing '{input_path}'. Error: {e}\n"
            #     )
            # else:
                # 记录成功
                success_logger.info(f"Successfully processed: {input_path}")
                if ts != "00:00:00":
                    print(f"✓ 找到有效几何形状的时间戳：{ts}")
                else:
                    print(f"⚠️ 未找到有效几何形状")
