#!/usr/bin/env python3
"""
测试模型初始化
"""

import sys
import os
import time

print("测试模型初始化...")

# 测试RetinaFace模型初始化
try:
    print("正在初始化RetinaFace模型...")
    start_time = time.time()
    
    from models.pipeline import RetinaFaceDetectionPipeline
    model_path = 'models/cv_resnet50_face-detection_retinaface'
    device = 'cuda'
    
    detector = RetinaFaceDetectionPipeline(model_path, device=device)
    
    init_time = time.time() - start_time
    print(f"✓ RetinaFace模型初始化成功，耗时: {init_time:.2f}秒")
    
    # 测试简单推理
    import numpy as np
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    print("测试推理...")
    start_time = time.time()
    result = detector(test_image)
    inference_time = time.time() - start_time
    
    print(f"✓ 推理成功，耗时: {inference_time:.3f}秒")
    print(f"结果类型: {type(result)}")
    print(f"结果键: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
    
except Exception as e:
    print(f"❌ RetinaFace模型测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n模型初始化测试完成")
