import os

def process_fix_paths(txt_path, output_path):
    """
    读取 txt_path 文件，将每行存为列表，遍历寻找包含 “)_fix” 的行，
    将其视为路径，提取文件名（无后缀），然后打印文件名的 [:2] 和 [-14:] 部分。
    """
    # 读取文件，去除每行首尾空白字符
    with open(txt_path, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f]

    keywords = set()
    for line in lines:
        # 如果行中包含 “)_fix”
        if ")_fix" in line:
            # 从路径中提取文件名
            basename = os.path.basename(line)           # e.g. "example_fix.txt"
            name, _ext = os.path.splitext(basename)     # e.g. ("example_fix", ".txt")

            print(name[2:-14])
            keywords.add(name[2:-14])

    filtered = []
    for line in lines:
        # 如果这一行不包含任一关键词，则保留
        if not any(kw in line for kw in keywords):
            filtered.append(line)

        with open(output_path, 'w', encoding='utf-8') as out_f:
            for l in filtered:
                out_f.write(l + '\n')

process_fix_paths("/home/<USER>/video_summerization/fync/processed.log", "/home/<USER>/video_summerization/fync/processed_new.log")