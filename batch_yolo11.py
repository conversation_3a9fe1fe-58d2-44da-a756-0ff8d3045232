import cv2
import time
import os
import argparse
import logging
from tqdm import tqdm
import json
import sys
import numpy as np
import math
from math import cos, sin

# PyTorch and related imports
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
from torchvision import transforms
import torchvision
from PIL import Image

# YOLO and Hugging Face imports
from ultralytics import YOLO
from huggingface_hub import hf_hub_download

# CVCUDA imports
import cvcuda
import nvcv
SCORE_THRESHOLD = 0.75
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

# Add deep-head-pose code directory to path for Hopenet
deep_head_pose_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/code'
if deep_head_pose_path not in sys.path:
    sys.path.append(deep_head_pose_path)

# Import Hopenet modules
try:
    import hopenet
    import utils
    HOPENET_AVAILABLE = True
    print("✓ Hopenet dependencies loaded successfully")
except ImportError as e:
    print(f"⚠️ Hopenet dependencies not available: {e}")
    HOPENET_AVAILABLE = False

# def check_frame(frame):
#     brightness, contrast_std, sharpness = check_brightness_and_contrast(frame)

#     scores = None
#     boxes = None

#     if brightness > 10 and contrast_std > 5:
#         results = model(frame)          # 推理

#         res = results[0]      # First (and only) results instance
#         # Extract boxes, confidences, classes
#         scores = res.boxes.conf.cpu().numpy()     # shape (N,) confidence scores :contentReference[oaicite:7]{index=7}
#         boxes = res.boxes.xyxy.cpu().numpy()      # shape (N, 4): x1, y1, x2, y2 :contentReference[oaicite:6]{index=6}

#     return brightness, contrast_std, sharpness, scores, boxes

def stitch_images(image_list, grid_shape=(5, 6), save_path='stitched.jpg'):
    """
    Stitch a list of OpenCV images into a single large image and save it to disk.

    Parameters:
    - image_list: list of cv2 images (numpy arrays)
    - grid_shape: tuple (rows, cols) indicating how to arrange the images
    - save_path: file path to save the stitched image

    Returns:
    - stitched_image: the resulting large image as a numpy array
    """
    num_images = len(image_list)
    rows, cols = grid_shape

    if num_images != rows * cols:
        raise ValueError(f"Number of images ({num_images}) does not match grid shape {grid_shape}")

    # Ensure all images have the same size
    heights = [img.shape[0] for img in image_list]
    widths = [img.shape[1] for img in image_list]
    min_height, min_width = min(heights), min(widths)

    # Resize images to the smallest dimensions to keep consistency
    resized = [cv2.resize(img, (min_width, min_height)) for img in image_list]

    # Prepare a blank canvas
    stitched_height = rows * min_height
    stitched_width = cols * min_width
    # Determine number of channels (grayscale vs colour)
    channels = 1 if len(resized[0].shape) == 2 else resized[0].shape[2]
    stitched_image = np.zeros((stitched_height, stitched_width, channels), dtype=resized[0].dtype)

    # Place each image in the grid
    idx = 0
    for r in range(rows):
        for c in range(cols):
            y0 = r * min_height
            y1 = y0 + min_height
            x0 = c * min_width
            x1 = x0 + min_width
            stitched_image[y0:y1, x0:x1] = resized[idx]
            idx += 1

    # Save the result
    cv2.imwrite(save_path, stitched_image)

    return stitched_image

def check_brightness_and_contrast(img):
    # 3. 转为 HWC 布局，确保内存连续
    img_hwc: torch.Tensor = torch.from_numpy(img).contiguous().to("cuda")

    # 4. 包装为 CVCUDA Tensor，布局声明为 "HWC"
    img_tensor = cvcuda.as_tensor(img_hwc, layout="HWC")  # :contentReference[oaicite:7]{index=7}

    gray = cvcuda.cvtcolor(img_tensor, cvcuda.ColorConversion.BGR2GRAY)

    # 3. 转 PyTorch GPU Tensor 并计算平均亮度
    #    转为 [H,W] 后转 float
    gray_t = torch.as_tensor(gray.cuda()).squeeze(-1).float() * 100.0 / 255.0
    brightness = torch.mean(gray_t)  # 全局平均亮度

    # 4. 对比度图：|pixel - brightness|
    contrast_map = torch.abs(gray_t - brightness)  #
    contrast_std = torch.std(contrast_map)

    # 5. 清晰度评估：Laplacian + 方差
    lap = cvcuda.laplacian(gray, ksize=3, scale=1.0)
    lap_t = torch.as_tensor(lap.cuda()).float()
    sharpness = torch.var(lap_t)  # 方差代表高频成分强度
    return brightness, contrast_std, sharpness

# Download fine‑tuned YOLOv8 face‑detection weights from Hugging Face
model_path = hf_hub_download(repo_id="AdamCodd/YOLOv11n-face-detection", filename="model.pt")  # Face‑detection weights (~16 MB) :contentReference[oaicite:2]{index=2}

# Load the YOLOv8 model
model = YOLO(model_path)  # Ultralytics API loads PyTorch/ONNX seamlessly :contentReference[oaicite:3]{index=3}

def find_first_face_timestamp(video_path: str, save_image_path: str) -> str:
    """
    每隔1秒抽帧，用 YuNet 快速检测人脸。首次检测到时：
      1. 保存该帧到 save_image_path；
      2. 返回 HH:MM:SS 时间戳。
    若视频末尾仍未检测到，返回 "00:00:00" 且不保存。

    :param video_path: 视频文件路径
    :param save_image_path: 检测到人脸时，保存帧图像的路径
    :return: FFmpeg 支持的时间戳字符串 "HH:MM:SS"
    """
    if not os.path.isfile(video_path):
        raise FileNotFoundError(f"视频文件不存在: {video_path}")

    # 确保保存目录存在
    save_dir = os.path.dirname(save_image_path)
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise IOError(f"无法打开视频: {video_path}")

    # 获取视频基本参数 :contentReference[oaicite:0]{index=0}
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    sample_interval_sec=3

    # 抽帧步长 = fps × sample_interval_sec
    sample_step = max(1, int(fps * sample_interval_sec))

    global_idx = 0  # 全局帧计数

    while True:
        ret, frame = cap.read()

        if not ret:
            break  # 视频结束

        if global_idx % sample_step == 0:
            b, c, s = check_brightness_and_contrast(frame)
            if b > 10 and c > 5:
                start_time = time.time()


                h, w = frame.shape[:2]
                # Compute side length
                # side = int(min(h, w) * 0.75)
                side = int(min(h, w))

                # # Compute top-left corner of the square
                x_offset = (w - side) // 2
                y_offset = (h - side) // 2

                # x_offset = int(w/8)
                # y_offset = int(h/8)

                # Crop and return
                cropped_frame = frame[y_offset:y_offset + side, x_offset:x_offset + side]
                # cropped_frame = frame[int(h/8):int(h*7/8), int(w/8):int(w*7/8)]

                results = model(cropped_frame)          # 推理

                res = results[0]      # First (and only) results instance
                # Extract boxes, confidences, classes
                scores = res.boxes.conf.cpu().numpy()     # shape (N,) confidence scores :contentReference[oaicite:7]{index=7}
                boxes = res.boxes.xyxy.cpu().numpy()      # shape (N, 4): x1, y1, x2, y2 :contentReference[oaicite:6]{index=6}

                # 在boxes中过滤掉长、宽小于30像素的检测框
                boxes = boxes[np.logical_or(boxes[:, 2] - boxes[:, 0] > 30, boxes[:, 3] - boxes[:, 1] > 30)]
                scores = scores[np.logical_or(boxes[:, 2] - boxes[:, 0] > 30, boxes[:, 3] - boxes[:, 1] > 30)]

                # 在boxes中过滤掉长、宽小于画面10%的检测框
                boxes = boxes[np.logical_or(boxes[:, 2] - boxes[:, 0] > w*0.1, boxes[:, 3] - boxes[:, 1] > h*0.1)]
                scores = scores[np.logical_or(boxes[:, 2] - boxes[:, 0] > w*0.1, boxes[:, 3] - boxes[:, 1] > h*0.1)]


            # brightness, contrast_std, sharpness, boxes, scores
                if scores is not None and len(scores) > 0 and max(scores)>SCORE_THRESHOLD:

                    # Process each detected face
                    for i, ((x1, y1, x2, y2), conf) in enumerate(zip(boxes, scores)):
                        # Convert coords to int and adjust for cropping offset
                        x1, y1, x2, y2 = map(int, (x1+x_offset, y1+y_offset, x2+x_offset, y2+y_offset))

                        # Estimate head pose for this face
                        pose_angles = estimate_head_pose(frame, (x1, y1, x2, y2))

                        inference_time = (time.time() - start_time) * 1000  # 转为毫秒

                        # Draw bounding box
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 3)

                        if pose_angles is not None:
                            # Calculate face deviation
                            deviation = calculate_face_deviation(pose_angles)

                            # Draw 3D coordinate axes
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            bbox_height = y2 - y1
                            axis_size = bbox_height // 3

                            draw_axis(frame, pose_angles['yaw'], pose_angles['pitch'], pose_angles['roll'],
                                    tdx=center_x, tdy=center_y, size=axis_size)

                            # Display pose information
                            pose_text = f"Y:{pose_angles['yaw']:.1f} P:{pose_angles['pitch']:.1f} R:{pose_angles['roll']:.1f}"
                            cv2.putText(frame, pose_text, (x1, y1 - 30),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

                            # Display deviation in top-left corner with large, prominent text
                            if deviation is not None:
                                deviation_text = f"{deviation:.1f} deg"
                                # Use large font and bright color for visibility
                                cv2.putText(frame, deviation_text, (30, 140),
                                           cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 255), 4)  # Yellow text
                                # Add background rectangle for better visibility
                                text_size = cv2.getTextSize(deviation_text, cv2.FONT_HERSHEY_SIMPLEX, 3, 4)[0]
                                cv2.rectangle(frame, (25, 100), (35 + text_size[0], 150), (0, 0, 0), -1)
                                cv2.putText(frame, deviation_text, (30, 140),
                                           cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 255), 4)

                        # Put confidence text
                        label = f"Face {i+1}: {conf:.2f}"
                        cv2.putText(frame, label, (x1, y1 - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                    # 在左上角叠加推理耗时
                    time_label = f"{inference_time:.1f} ms"
                    cv2.putText(frame, time_label, (30, 70),
                                cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)

                    # 保存当前帧
                    cv2.imwrite(save_image_path, frame)
                    timestamp = time.strftime("%H:%M:%S", time.gmtime(global_idx / fps))

                    # fs = []
                    # for idx in range(int(5*fps)):
                    #     ret, frame = cap.read()
                    #     if not ret:
                    #         break  # 视频结束

                    #     if idx % 3 == 0:
                    #         b, c, s = check_brightness_and_contrast(frame)
                    #         cv2.putText(frame, f"{int(b)} {int(c)}", (30, 70),
                    #                     cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)
                    #         cv2.putText(frame, f"{int(s)}", (30, 140),
                    #                     cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)
                    #         fs.append(frame)
                    # stitch_images(fs[:30], save_path=save_image_path+".jpg")

                    cap.release()
                    return timestamp

        global_idx += 1

        if not ret or global_idx >= total_frames:
            break  # 全部读取完毕

    cap.release()
    return "00:00:00"

def process_videos(input_base: str, output_base: str):
    # Collect all video files first
    video_paths = []
    for root, _, files in os.walk(input_base):
        for fname in files:
            if fname.lower().endswith(VIDEO_EXTENSIONS):
                video_paths.append(os.path.join(root, fname))

    # Ensure base output directory exists
    os.makedirs(output_base, exist_ok=True)

    # Path for record of processed files
    processed_file = os.path.join(output_base, 'processed.log')
    # Load already processed files
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # Filter out already processed videos
    pending_paths = [p for p in video_paths if p not in processed_set]

    # Setup separate loggers for success and error
    success_log = os.path.join(output_base, 'success.log')
    error_log = os.path.join(output_base, 'error.log')

    # Error logger
    error_logger = logging.getLogger('whisperx_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    # Success logger
    success_logger = logging.getLogger('whisperx_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    # Process with progress bar
    for input_path in tqdm(pending_paths, desc="Processing videos", unit="file"):
        # Determine output directory based on relative path and filename
        root = os.path.dirname(input_path)
        fname = os.path.basename(input_path)
        rel_dir = os.path.relpath(root, input_base)
        base_name, _ = os.path.splitext(fname)
        output_dir = os.path.join(output_base, rel_dir)
        output_jpg = os.path.join(output_dir, base_name+".jpg")
        os.makedirs(output_dir, exist_ok=True)

        try:
            ts = find_first_face_timestamp(input_path, output_jpg)
        except Exception as e:
            error_logger.error(
                f"Failed processing '{input_path}'. Return: {e}\n"
            )
        else:
            # Log success and mark as processed
            success_logger.info(f"Successfully processed: {input_path}")
            print(f"首次检测到人脸的时间戳：{ts}")

def print_visualization_legend():
    """打印可视化说明"""
    print("\n" + "=" * 80)
    print("YOLO11 + Hopenet 人脸检测与朝向分析可视化说明:")
    print("=" * 80)
    print("- 绿色矩形框: 人脸边界框")
    print("- 红色线条: X轴 (左右方向)")
    print("- 绿色线条: Y轴 (上下方向)")
    print("- 蓝色线条: Z轴 (前后方向)")
    print("- 黄色文字: 姿态角度 (Yaw/Pitch/Roll)")
    print("- 左上角大号黄色数字: 人脸偏离度 (度数，越小越正对屏幕)")
    print("- Face ID + 置信度: 人脸编号和检测置信度")
    print("=" * 80)

def main():
    parser = argparse.ArgumentParser(
        description='Batch process videos with YOLO11 face detection and Hopenet head pose estimation.'
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )

    args = parser.parse_args()

    print("=" * 80)
    print("YOLO11 + Hopenet 批量视频人脸检测与朝向分析程序")
    print("=" * 80)
    print(f"输入目录: {args.input_base}")
    print(f"输出目录: {args.output_base}")

    # 显示可视化说明
    print_visualization_legend()

    process_videos(args.input_base, args.output_base)

    print("=" * 80)
    print("批量处理完成！")
    print("=" * 80)

    # 再次显示可视化说明
    print_visualization_legend()


# Initialize Hopenet model for head pose estimation
hopenet_model = None
hopenet_transformations = None
idx_tensor = None
device = 'cuda' if torch.cuda.is_available() else 'cpu'

def initialize_hopenet():
    """Initialize Hopenet model for head pose estimation"""
    global hopenet_model, hopenet_transformations, idx_tensor

    if not HOPENET_AVAILABLE:
        print("⚠️ Hopenet not available, skipping head pose estimation")
        return False

    try:
        print("正在初始化Hopenet模型...")

        # Initialize Hopenet model
        hopenet_model = hopenet.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)

        # Load model weights
        hopenet_weights_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/hopenet_robust_alpha1.pkl'
        if os.path.exists(hopenet_weights_path):
            print(f"加载Hopenet权重: {hopenet_weights_path}")
            saved_state_dict = torch.load(hopenet_weights_path, map_location=device)
            hopenet_model.load_state_dict(saved_state_dict)
        else:
            print("⚠️ Hopenet权重文件未找到，使用随机权重")

        # Move model to device
        if torch.cuda.is_available():
            hopenet_model.cuda()
        hopenet_model.eval()

        # Setup transformations
        hopenet_transformations = transforms.Compose([
            transforms.Resize(224),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # Setup index tensor for pose calculation
        idx_list = [idx for idx in range(66)]
        if torch.cuda.is_available():
            idx_tensor = torch.FloatTensor(idx_list).cuda()
        else:
            idx_tensor = torch.FloatTensor(idx_list)

        print("✓ Hopenet模型初始化完成")
        return True

    except Exception as e:
        print(f"❌ Hopenet模型初始化失败: {e}")
        return False

def expand_bbox_dockerface_style(x_min, y_min, x_max, y_max, img_width, img_height, expansion=50):
    """Expand bounding box using dockerface-style expansion"""
    # Apply fixed pixel expansion
    x_min -= expansion
    x_max += expansion
    y_min -= expansion
    y_max += int(expansion * 0.6)  # Less expansion on bottom

    # Ensure coordinates are within image bounds
    x_min = max(x_min, 0)
    y_min = max(y_min, 0)
    x_max = min(img_width, x_max)
    y_max = min(img_height, y_max)

    return x_min, y_min, x_max, y_max

def estimate_head_pose(image, bbox):
    """Estimate head pose for a face bounding box"""
    if hopenet_model is None or hopenet_transformations is None:
        return None

    try:
        x_min, y_min, x_max, y_max = map(int, bbox)
        img_height, img_width = image.shape[:2]

        # Expand bounding box
        expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max = expand_bbox_dockerface_style(
            x_min, y_min, x_max, y_max, img_width, img_height
        )

        # Crop face region
        face_img = image[expanded_y_min:expanded_y_max, expanded_x_min:expanded_x_max]

        if face_img.size == 0:
            return None

        # Convert to RGB for PIL
        face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
        face_pil = Image.fromarray(face_rgb)

        # Apply transformations
        transformed_img = hopenet_transformations(face_pil)
        img_shape = transformed_img.size()
        transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])

        # Move to device
        if torch.cuda.is_available():
            transformed_img = Variable(transformed_img).cuda()
        else:
            transformed_img = Variable(transformed_img)

        # Forward pass through Hopenet
        with torch.no_grad():
            yaw, pitch, roll = hopenet_model(transformed_img)

            # Apply softmax and get predictions
            yaw_predicted = F.softmax(yaw, dim=1)
            pitch_predicted = F.softmax(pitch, dim=1)
            roll_predicted = F.softmax(roll, dim=1)

            # Get continuous predictions in degrees
            yaw_predicted = torch.sum(yaw_predicted.data[0] * idx_tensor) * 3 - 99
            pitch_predicted = torch.sum(pitch_predicted.data[0] * idx_tensor) * 3 - 99
            roll_predicted = torch.sum(roll_predicted.data[0] * idx_tensor) * 3 - 99

            # Convert to float
            yaw_deg = float(yaw_predicted)
            pitch_deg = float(pitch_predicted)
            roll_deg = float(roll_predicted)

            return {
                'yaw': yaw_deg,
                'pitch': pitch_deg,
                'roll': roll_deg
            }

    except Exception as e:
        print(f"头部姿态估计错误: {e}")
        return None

def calculate_face_deviation(pose_angles):
    """Calculate face deviation from screen perpendicular"""
    if pose_angles is None:
        return None

    yaw = pose_angles['yaw']
    pitch = pose_angles['pitch']
    roll = pose_angles['roll']

    # Calculate deviation from vertical (Z-axis perpendicular to screen)
    # For a face looking straight at camera: yaw≈0, pitch≈0, roll≈0
    deviation = np.sqrt(yaw**2 + pitch**2 + roll**2)

    # Clamp to reasonable range
    deviation = min(deviation, 90.0)

    return deviation

def draw_axis(img, yaw, pitch, roll, tdx=None, tdy=None, size=100):
    """Draw 3D coordinate axes on image"""
    pitch = pitch * np.pi / 180
    yaw = -(yaw * np.pi / 180)
    roll = roll * np.pi / 180

    if tdx is not None and tdy is not None:
        tdx = tdx
        tdy = tdy
    else:
        height, width = img.shape[:2]
        tdx = width // 2
        tdy = height // 2

    # X-Axis pointing to right, drawn in red
    x1 = size * (cos(yaw) * cos(roll)) + tdx
    y1 = size * (cos(pitch) * sin(roll) + cos(roll) * sin(pitch) * sin(yaw)) + tdy

    # Y-Axis drawn in green
    x2 = size * (-cos(yaw) * sin(roll)) + tdx
    y2 = size * (cos(pitch) * cos(roll) - sin(pitch) * sin(yaw) * sin(roll)) + tdy

    # Z-Axis (out of the screen) drawn in blue
    x3 = size * (sin(yaw)) + tdx
    y3 = size * (-cos(yaw) * sin(pitch)) + tdy

    cv2.line(img, (int(tdx), int(tdy)), (int(x1), int(y1)), (0, 0, 255), 3)  # Red X-axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x2), int(y2)), (0, 255, 0), 3)  # Green Y-axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x3), int(y3)), (255, 0, 0), 2)  # Blue Z-axis

    return img

if __name__ == '__main__':
    # Initialize Hopenet model
    initialize_hopenet()
    main()

