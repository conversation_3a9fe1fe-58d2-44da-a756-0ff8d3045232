#!/usr/bin/env python3
"""
基于RetinaFace的人脸几何形状验证脚本

新增功能：
1. 判断左眼-右眼-右嘴角-左嘴角-左眼是否构成单一封闭四边形
2. 判断鼻子是否在上述封闭四边形内部
3. 在图片右上角显示两个准则的判别结果（T/F）
4. 若准则未全部通过，继续分析直至找到第一个全部通过的帧

基于：batch_retinaface.py
作者：基于原始脚本改进
"""

import cv2
import time
import os
import argparse
import logging
from tqdm import tqdm
import json
import numpy as np
import sys
import math
from math import cos, sin
from shapely.geometry import Point, Polygon
from shapely.validation import explain_validity

# PyTorch and related imports
import cvcuda
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
from torchvision import transforms
import torchvision
from PIL import Image

# RetinaFace相关导入 - 使用本地模型
from models.retinaface.detection import RetinaFaceDetection
from models.utils import LoadImage

# ============================================================================
# 全局配置
# ============================================================================
SCORE_THRESHOLD = 0.98
ENABLE_HOPENET = False  # 默认禁用HopeNet功能，可通过命令行参数启用
HOPENET_AVAILABLE = True

# 几何验证相关配置
GEOMETRY_CHECK_ENABLED = True  # 启用几何形状验证
GEOMETRY_TOLERANCE = 1e-6      # 几何计算容差

# 只有在启用HopeNet时才尝试加载相关依赖
if ENABLE_HOPENET:
    # Add deep-head-pose code directory to path for Hopenet
    deep_head_pose_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/code'
    if deep_head_pose_path not in sys.path:
        sys.path.append(deep_head_pose_path)

    # Import Hopenet modules
    try:
        import hopenet
        import utils
        HOPENET_AVAILABLE = True
        print("✓ Hopenet dependencies loaded successfully")
    except ImportError as e:
        print(f"⚠️ Hopenet dependencies not available: {e}")
        HOPENET_AVAILABLE = False
else:
    print("ℹ️ HopeNet人脸朝向检测功能已禁用")
    print("   专注于人脸几何形状验证功能")

VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

# HopeNet相关全局变量
hopenet_model = None
hopenet_transformations = None
idx_tensor = None
device = 'cuda' if torch.cuda.is_available() else 'cpu'


def initialize_hopenet():
    """Initialize Hopenet model for head pose estimation"""
    global hopenet_model, hopenet_transformations, idx_tensor

    if not ENABLE_HOPENET:
        print("ℹ️ HopeNet功能已禁用，跳过初始化")
        return False

    if not HOPENET_AVAILABLE:
        print("⚠️ Hopenet not available, skipping head pose estimation")
        return False

    try:
        print("正在初始化Hopenet模型...")

        # Initialize Hopenet model
        hopenet_model = hopenet.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)

        # Load model weights
        hopenet_weights_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/hopenet_robust_alpha1.pkl'
        if os.path.exists(hopenet_weights_path):
            print(f"加载Hopenet权重: {hopenet_weights_path}")
            saved_state_dict = torch.load(hopenet_weights_path, map_location=device)
            hopenet_model.load_state_dict(saved_state_dict)
        else:
            print("⚠️ Hopenet权重文件未找到，使用随机权重")

        # Move model to device
        if torch.cuda.is_available():
            hopenet_model.cuda()
        hopenet_model.eval()

        # Setup transformations
        hopenet_transformations = transforms.Compose([
            transforms.Resize(224),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # Setup index tensor for pose calculation
        idx_list = [idx for idx in range(66)]
        if torch.cuda.is_available():
            idx_tensor = torch.FloatTensor(idx_list).cuda()
        else:
            idx_tensor = torch.FloatTensor(idx_list)

        print("✓ Hopenet模型初始化完成")
        return True

    except Exception as e:
        print(f"❌ Hopenet模型初始化失败: {e}")
        return False


def expand_bbox_dockerface_style(x_min, y_min, x_max, y_max, img_width, img_height, expansion=50):
    """Expand bounding box using dockerface-style expansion"""
    # Apply fixed pixel expansion
    x_min -= expansion
    x_max += expansion
    y_min -= expansion
    y_max += int(expansion * 0.6)  # Less expansion on bottom

    # Ensure coordinates are within image bounds
    x_min = max(x_min, 0)
    y_min = max(y_min, 0)
    x_max = min(img_width, x_max)
    y_max = min(img_height, y_max)

    return x_min, y_min, x_max, y_max


def estimate_head_pose(image, bbox):
    """Estimate head pose for a face bounding box"""
    if not ENABLE_HOPENET:
        return None

    if hopenet_model is None or hopenet_transformations is None:
        return None

    try:
        x_min, y_min, x_max, y_max = map(int, bbox)
        img_height, img_width = image.shape[:2]

        # Expand bounding box
        expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max = expand_bbox_dockerface_style(
            x_min, y_min, x_max, y_max, img_width, img_height
        )

        # Crop face region
        face_img = image[expanded_y_min:expanded_y_max, expanded_x_min:expanded_x_max]

        if face_img.size == 0:
            return None

        # Convert to RGB for PIL
        face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
        face_pil = Image.fromarray(face_rgb)

        # Apply transformations
        transformed_img = hopenet_transformations(face_pil)
        img_shape = transformed_img.size()
        transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])

        # Move to device
        if torch.cuda.is_available():
            transformed_img = Variable(transformed_img).cuda()
        else:
            transformed_img = Variable(transformed_img)

        # Forward pass through Hopenet
        with torch.no_grad():
            yaw, pitch, roll = hopenet_model(transformed_img)

            # Apply softmax and get predictions
            yaw_predicted = F.softmax(yaw, dim=1)
            pitch_predicted = F.softmax(pitch, dim=1)
            roll_predicted = F.softmax(roll, dim=1)

            # Get continuous predictions in degrees
            yaw_predicted = torch.sum(yaw_predicted.data[0] * idx_tensor) * 3 - 99
            pitch_predicted = torch.sum(pitch_predicted.data[0] * idx_tensor) * 3 - 99
            roll_predicted = torch.sum(roll_predicted.data[0] * idx_tensor) * 3 - 99

            # Convert to float
            yaw_deg = float(yaw_predicted)
            pitch_deg = float(pitch_predicted)
            roll_deg = float(roll_predicted)

            return {
                'yaw': yaw_deg,
                'pitch': pitch_deg,
                'roll': roll_deg
            }

    except Exception as e:
        print(f"头部姿态估计错误: {e}")
        return None


def calculate_face_deviation(pose_angles):
    """Calculate face deviation from screen perpendicular"""
    if not ENABLE_HOPENET or pose_angles is None:
        return None

    yaw = pose_angles['yaw']
    pitch = pose_angles['pitch']
    roll = pose_angles['roll']

    # Calculate deviation from vertical (Z-axis perpendicular to screen)
    # For a face looking straight at camera: yaw≈0, pitch≈0, roll≈0
    deviation = np.sqrt(yaw**2 + pitch**2 + roll**2)

    # Clamp to reasonable range
    deviation = min(deviation, 90.0)

    return deviation


def draw_axis(img, yaw, pitch, roll, tdx=None, tdy=None, size=100):
    """Draw 3D coordinate axes on image"""
    if not ENABLE_HOPENET:
        return img

    pitch = pitch * np.pi / 180
    yaw = -(yaw * np.pi / 180)
    roll = roll * np.pi / 180

    if tdx is not None and tdy is not None:
        tdx = tdx
        tdy = tdy
    else:
        height, width = img.shape[:2]
        tdx = width // 2
        tdy = height // 2

    # X-Axis pointing to right, drawn in red
    x1 = size * (cos(yaw) * cos(roll)) + tdx
    y1 = size * (cos(pitch) * sin(roll) + cos(roll) * sin(pitch) * sin(yaw)) + tdy

    # Y-Axis drawn in green
    x2 = size * (-cos(yaw) * sin(roll)) + tdx
    y2 = size * (cos(pitch) * cos(roll) - sin(pitch) * sin(yaw) * sin(roll)) + tdy

    # Z-Axis (out of the screen) drawn in blue
    x3 = size * (sin(yaw)) + tdx
    y3 = size * (-cos(yaw) * sin(pitch)) + tdy

    cv2.line(img, (int(tdx), int(tdy)), (int(x1), int(y1)), (0, 0, 255), 3)  # Red X-axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x2), int(y2)), (0, 255, 0), 3)  # Green Y-axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x3), int(y3)), (255, 0, 0), 2)  # Blue Z-axis

    return img


class FaceGeometryValidator:
    """人脸几何形状验证器"""

    def __init__(self):
        """初始化验证器"""
        self.tolerance = GEOMETRY_TOLERANCE
        print("✓ 人脸几何形状验证器初始化完成")

    def parse_landmarks(self, landmarks):
        """
        解析RetinaFace关键点

        Args:
            landmarks: RetinaFace输出的关键点列表 [x1, y1, x2, y2, ..., x5, y5]

        Returns:
            dict: 解析后的关键点字典
        """
        if landmarks is None or len(landmarks) < 10:
            return None

        # RetinaFace关键点顺序：左眼、右眼、鼻子、左嘴角、右嘴角
        points = {
            'left_eye': (landmarks[0], landmarks[1]),
            'right_eye': (landmarks[2], landmarks[3]),
            'nose': (landmarks[4], landmarks[5]),
            'left_mouth': (landmarks[6], landmarks[7]),
            'right_mouth': (landmarks[8], landmarks[9])
        }

        return points

    def check_quadrilateral_validity(self, points):
        """
        检查左眼-右眼-右嘴角-左嘴角-左眼是否构成单一封闭四边形

        Args:
            points (dict): 关键点字典

        Returns:
            bool: 是否构成有效四边形
        """
        try:
            # 构建四边形顶点序列：左眼 -> 右眼 -> 右嘴角 -> 左嘴角 -> 左眼
            quad_points = [
                points['left_eye'],
                points['right_eye'],
                points['right_mouth'],
                points['left_mouth']
            ]

            # 使用Shapely创建多边形
            polygon = Polygon(quad_points)

            # 检查多边形是否有效（无自相交、无重复点等）
            is_valid = polygon.is_valid

            if not is_valid:
                # 输出详细的无效原因（调试用）
                validity_reason = explain_validity(polygon)
                print(f"四边形无效: {validity_reason}")

            return is_valid

        except Exception as e:
            print(f"四边形验证错误: {e}")
            return False

    def check_nose_inside_quadrilateral(self, points):
        """
        检查鼻子是否在四边形内部

        Args:
            points (dict): 关键点字典

        Returns:
            bool: 鼻子是否在四边形内部
        """
        try:
            # 构建四边形
            quad_points = [
                points['left_eye'],
                points['right_eye'],
                points['right_mouth'],
                points['left_mouth']
            ]

            polygon = Polygon(quad_points)

            # 检查多边形是否有效
            if not polygon.is_valid:
                return False

            # 创建鼻子点
            nose_point = Point(points['nose'])

            # 检查鼻子是否在多边形内部（不包括边界）
            return polygon.contains(nose_point)

        except Exception as e:
            print(f"鼻子位置验证错误: {e}")
            return False

    def validate_face_geometry(self, landmarks):
        """
        验证人脸几何形状

        Args:
            landmarks: RetinaFace关键点

        Returns:
            tuple: (是否为有效四边形, 鼻子是否在内部)
        """
        points = self.parse_landmarks(landmarks)
        if not points:
            return False, False

        # 检查1: 四边形有效性
        is_valid_quad = self.check_quadrilateral_validity(points)

        # 检查2: 鼻子在内部
        nose_inside = False
        if is_valid_quad:
            nose_inside = self.check_nose_inside_quadrilateral(points)

        return is_valid_quad, nose_inside


def draw_geometry_validation_result(frame, is_valid_quad, nose_inside, x_offset=0, y_offset=0):
    """
    在图片右上角绘制几何验证结果

    Args:
        frame: 图像帧
        is_valid_quad: 四边形是否有效
        nose_inside: 鼻子是否在内部
        x_offset: X坐标偏移（用于裁剪图像的坐标调整）
        y_offset: Y坐标偏移
    """
    h, w = frame.shape[:2]

    # 在右上角显示结果
    result_text_1 = f"Quad: {'T' if is_valid_quad else 'F'}"
    result_text_2 = f"Nose: {'T' if nose_inside else 'F'}"

    # 计算文本位置（右上角）
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1.5
    thickness = 3

    # 获取文本尺寸
    (text_w1, text_h1), _ = cv2.getTextSize(result_text_1, font, font_scale, thickness)
    (text_w2, text_h2), _ = cv2.getTextSize(result_text_2, font, font_scale, thickness)

    # 计算位置
    margin = 20
    x1 = w - max(text_w1, text_w2) - margin
    y1 = margin + text_h1
    y2 = y1 + text_h2 + 10

    # 绘制背景矩形
    bg_x1 = x1 - 10
    bg_y1 = margin - 5
    bg_x2 = w - margin + 5
    bg_y2 = y2 + 10
    cv2.rectangle(frame, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)  # 黑色背景

    # 绘制文本
    color1 = (0, 255, 0) if is_valid_quad else (0, 0, 255)  # 绿色/红色
    color2 = (0, 255, 0) if nose_inside else (0, 0, 255)    # 绿色/红色

    cv2.putText(frame, result_text_1, (x1, y1), font, font_scale, color1, thickness)
    cv2.putText(frame, result_text_2, (x1, y2), font, font_scale, color2, thickness)


def draw_face_quadrilateral(frame, landmarks, x_offset=0, y_offset=0):
    """
    绘制人脸四边形和关键点

    Args:
        frame: 图像帧
        landmarks: 关键点
        x_offset: X坐标偏移
        y_offset: Y坐标偏移
    """
    if landmarks is None or len(landmarks) < 10:
        return

    # 解析关键点
    left_eye = (int(landmarks[0]) + x_offset, int(landmarks[1]) + y_offset)
    right_eye = (int(landmarks[2]) + x_offset, int(landmarks[3]) + y_offset)
    nose = (int(landmarks[4]) + x_offset, int(landmarks[5]) + y_offset)
    left_mouth = (int(landmarks[6]) + x_offset, int(landmarks[7]) + y_offset)
    right_mouth = (int(landmarks[8]) + x_offset, int(landmarks[9]) + y_offset)

    # 绘制四边形
    quad_points = np.array([left_eye, right_eye, right_mouth, left_mouth], np.int32)
    cv2.polylines(frame, [quad_points], True, (255, 255, 0), 2)  # 黄色四边形

    # 绘制关键点
    cv2.circle(frame, left_eye, 4, (255, 0, 0), -1)      # 蓝色左眼
    cv2.circle(frame, right_eye, 4, (255, 0, 0), -1)     # 蓝色右眼
    cv2.circle(frame, nose, 4, (0, 255, 255), -1)        # 黄色鼻子
    cv2.circle(frame, left_mouth, 4, (0, 0, 255), -1)    # 红色左嘴角
    cv2.circle(frame, right_mouth, 4, (0, 0, 255), -1)   # 红色右嘴角

    # 添加白色边框
    cv2.circle(frame, left_eye, 5, (255, 255, 255), 1)
    cv2.circle(frame, right_eye, 5, (255, 255, 255), 1)
    cv2.circle(frame, nose, 5, (255, 255, 255), 1)
    cv2.circle(frame, left_mouth, 5, (255, 255, 255), 1)
    cv2.circle(frame, right_mouth, 5, (255, 255, 255), 1)


def check_brightness_and_contrast(img):
    """检查图像亮度和对比度"""
    # 转为 HWC 布局，确保内存连续
    img_hwc: torch.Tensor = torch.from_numpy(img).contiguous().to("cuda")

    # 包装为 CVCUDA Tensor，布局声明为 "HWC"
    img_tensor = cvcuda.as_tensor(img_hwc, layout="HWC")

    gray = cvcuda.cvtcolor(img_tensor, cvcuda.ColorConversion.BGR2GRAY)

    # 转 PyTorch GPU Tensor 并计算平均亮度
    gray_t = torch.as_tensor(gray.cuda()).squeeze(-1).float() * 100.0 / 255.0
    brightness = torch.mean(gray_t)  # 全局平均亮度

    # 对比度图：|pixel - brightness|
    contrast_map = torch.abs(gray_t - brightness)
    contrast_std = torch.std(contrast_map)

    # 清晰度评估：Laplacian + 方差
    lap = cvcuda.laplacian(gray, ksize=3, scale=1.0)
    lap_t = torch.as_tensor(lap.cuda()).float()
    sharpness = torch.var(lap_t)  # 方差代表高频成分强度
    return brightness, contrast_std, sharpness


# 初始化RetinaFace检测器和几何验证器
print("正在初始化RetinaFace模型...")
model_path = 'models/cv_resnet50_face-detection_retinaface/pytorch_model.pt'
device = 'cuda' if torch.cuda.is_available() else 'cpu'
detector = RetinaFaceDetection(model_path, device=device)
print("✓ RetinaFace模型初始化完成")
geometry_validator = FaceGeometryValidator()


def find_first_valid_geometry_face(video_path: str, save_image_path: str) -> str:
    """
    每隔3秒抽帧，用 RetinaFace 检测人脸并验证几何形状。
    找到第一个通过几何验证的人脸时：
      1. 保存该帧到 save_image_path；
      2. 返回 HH:MM:SS 时间戳。
    若视频末尾仍未找到，返回 "00:00:00" 且不保存。

    在找到有效几何形状之前，会保存中间结果图片（文件名带_1, _2等后缀）

    :param video_path: 视频文件路径
    :param save_image_path: 检测到有效几何形状时，保存帧图像的路径
    :return: FFmpeg 支持的时间戳字符串 "HH:MM:SS"
    """
    if not os.path.isfile(video_path):
        raise FileNotFoundError(f"视频文件不存在: {video_path}")

    # 确保保存目录存在
    save_dir = os.path.dirname(save_image_path)
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise IOError(f"无法打开视频: {video_path}")

    # 获取视频基本参数
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    sample_interval_sec = 3
    sample_step = max(1, int(fps * sample_interval_sec))

    global_idx = 0
    attempt_count = 0  # 尝试次数计数器

    print("开始几何形状验证")

    while True:
        ret, frame = cap.read()

        if not ret:
            break  # 视频结束

        if global_idx % sample_step == 0:
            b, c, s = check_brightness_and_contrast(frame)
            # 将tensor转换为Python标量进行比较
            if float(b) > 10 and float(c) > 5:
                start_time = time.time()

                h, w = frame.shape[:2]
                # 计算正方形裁剪区域
                side = int(min(h, w))
                x_offset = (w - side) // 2
                y_offset = (h - side) // 2
                cropped_frame = frame[y_offset:y_offset + side, x_offset:x_offset + side]

                # 使用RetinaFace进行推理
                # 将numpy数组转换为模型输入格式
                input_data = {'img': cropped_frame}
                result = detector(input_data)

                # 直接解析RetinaFace检测结果
                if result is not None and len(result) == 2:
                    dets, landms = result

                    # 处理每个检测到的人脸
                    best_face = None
                    best_geometry_score = -1
                    best_confidence = 0

                    for i in range(len(dets)):
                        det = dets[i]
                        if len(det) < 5:
                            continue

                        x1, y1, x2, y2, confidence = det[:5]

                        if confidence < SCORE_THRESHOLD:
                            continue

                        # 过滤掉长、宽小于30像素的检测框
                        width = x2 - x1
                        height = y2 - y1
                        if width <= 30 or height <= 30:
                            continue

                        # 过滤掉长、宽小于画面10%的检测框
                        if width <= w*0.1 or height <= h*0.1:
                            continue

                        # 获取对应的关键点
                        landmarks = None
                        if landms is not None and i < len(landms):
                            landmarks = landms[i]

                        if landmarks is not None and confidence > 0.75:
                            # 进行几何形状验证
                            is_valid_quad, nose_inside = geometry_validator.validate_face_geometry(landmarks)

                            # 计算几何评分（两个条件都满足得2分，满足一个得1分）
                            geometry_score = int(is_valid_quad) + int(nose_inside)

                            if geometry_score > best_geometry_score or (geometry_score == best_geometry_score and confidence > best_confidence):
                                best_geometry_score = geometry_score
                                best_confidence = confidence
                                best_face = {
                                    'bbox': [x1, y1, x2, y2],
                                    'confidence': confidence,
                                    'landmarks': landmarks,
                                    'id': i + 1,
                                    'is_valid_quad': is_valid_quad,
                                    'nose_inside': nose_inside,
                                    'geometry_score': geometry_score
                                }

                    if best_face:
                        attempt_count += 1
                        inference_time = (time.time() - start_time) * 1000

                        # 获取最佳人脸的信息
                        bbox = best_face['bbox']
                        confidence = best_face['confidence']
                        landmarks = best_face['landmarks']
                        face_id = best_face['id']
                        is_valid_quad = best_face['is_valid_quad']
                        nose_inside = best_face['nose_inside']
                        geometry_score = best_face['geometry_score']

                        x1, y1, x2, y2 = map(int, bbox)

                        # 调整坐标到原图
                        x1, y1, x2, y2 = x1+x_offset, y1+y_offset, x2+x_offset, y2+y_offset

                        # 估计头部姿态（如果启用HopeNet）
                        pose_angles = None
                        deviation = None
                        if ENABLE_HOPENET:
                            pose_angles = estimate_head_pose(frame, (x1, y1, x2, y2))
                            if pose_angles:
                                deviation = calculate_face_deviation(pose_angles)

                        # 绘制边界框
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 3)

                        # 显示置信度和ID
                        label = f"Face {face_id}: {confidence:.3f}"
                        text_y = max(y1 - 10, 20)
                        cv2.putText(
                            frame, label, (x1, text_y),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2
                        )

                        # 绘制人脸四边形和关键点
                        draw_face_quadrilateral(frame, landmarks, x_offset, y_offset)

                        # 绘制HopeNet相关可视化（如果启用）
                        if ENABLE_HOPENET and pose_angles:
                            # 绘制3D坐标轴
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            bbox_height = y2 - y1
                            axis_size = bbox_height // 3

                            draw_axis(frame, pose_angles['yaw'], pose_angles['pitch'], pose_angles['roll'],
                                     tdx=center_x, tdy=center_y, size=axis_size)

                            # 显示姿态信息
                            pose_text = f"Y:{pose_angles['yaw']:.1f} P:{pose_angles['pitch']:.1f} R:{pose_angles['roll']:.1f}"
                            cv2.putText(frame, pose_text, (x1, y1 - 50),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

                        # 在右上角显示几何验证结果
                        draw_geometry_validation_result(frame, is_valid_quad, nose_inside)

                        # 在左上角显示推理耗时和尝试次数
                        time_label = f"{inference_time:.1f} ms (#{attempt_count})"
                        cv2.putText(frame, time_label, (30, 70),
                                    cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)

                        # 显示几何评分
                        score_label = f"Geo Score: {geometry_score}/2"
                        cv2.putText(frame, score_label, (30, 140),
                                    cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 0), 3)

                        # 显示人脸偏离度（如果启用HopeNet）
                        if ENABLE_HOPENET and deviation is not None:
                            deviation_text = f"{deviation:.1f} deg"
                            # 使用大字体和醒目颜色显示偏离度
                            text_size = cv2.getTextSize(deviation_text, cv2.FONT_HERSHEY_SIMPLEX, 3, 4)[0]
                            cv2.rectangle(frame, (25, 200), (35 + text_size[0], 250), (0, 0, 0), -1)  # 黑色背景
                            cv2.putText(frame, deviation_text, (30, 240),
                                       cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 255), 4)  # 黄色文字

                        # 决定是否保存图片
                        if geometry_score == 2:  # 两个条件都满足
                            # 保存最终成功的帧
                            cv2.imwrite(save_image_path, frame)
                            timestamp = time.strftime("%H:%M:%S", time.gmtime(global_idx / fps))
                            print(f"✓ 找到有效几何形状! 时间戳: {timestamp}, 尝试次数: {attempt_count}")
                            cap.release()
                            return timestamp
                        else:
                            # 保存中间结果（带序号后缀）
                            base_path, ext = os.path.splitext(save_image_path)
                            intermediate_path = f"{base_path}_{attempt_count}{ext}"
                            cv2.imwrite(intermediate_path, frame)
                            print(f"⚠️ 几何验证未通过 (评分: {geometry_score}/2)，保存中间结果: {intermediate_path}")

        global_idx += 1

        if not ret or global_idx >= total_frames:
            break  # 全部读取完毕

    cap.release()
    print(f"❌ 视频结束，未找到有效几何形状。总尝试次数: {attempt_count}")
    return "00:00:00"


def print_visualization_legend():
    """打印可视化说明"""
    print("\n" + "=" * 80)
    if ENABLE_HOPENET:
        print("RetinaFace + HopeNet 人脸几何形状验证与朝向分析可视化说明:")
    else:
        print("RetinaFace 人脸几何形状验证可视化说明:")
    print("=" * 80)
    print("- 绿色矩形框: 人脸边界框")
    print("- 蓝色圆点: 眼睛位置")
    print("- 黄色圆点: 鼻子位置")
    print("- 红色圆点: 嘴角位置")
    print("- 白色边框: 关键点轮廓")
    print("- 黄色线条: 左眼-右眼-右嘴角-左嘴角构成的四边形")
    print("- 右上角 Quad T/F: 四边形是否有效（无自相交）")
    print("- 右上角 Nose T/F: 鼻子是否在四边形内部")
    print("- 左上角推理时间和尝试次数")
    print("- Geo Score: 几何评分 (2/2为完全通过)")
    print("- Face ID + 置信度: 人脸编号和检测置信度")

    if ENABLE_HOPENET:
        print("- 红色线条: X轴 (左右方向)")
        print("- 绿色线条: Y轴 (上下方向)")
        print("- 蓝色线条: Z轴 (前后方向)")
        print("- 黄色文字: 姿态角度 (Yaw/Pitch/Roll)")
        print("- 左上角大号黄色数字: 人脸偏离度 (度数，越小越正对屏幕)")
    else:
        print("- HopeNet人脸朝向检测功能已禁用")
        print("- 如需启用朝向分析，请在代码中将 ENABLE_HOPENET 设置为 True")

    print("=" * 80)


def process_videos(input_base: str, output_base: str):
    """批量处理视频文件"""
    # 收集所有视频文件
    video_paths = []

    if os.path.isfile(input_base):
        # 如果输入是单个文件
        if input_base.lower().endswith(VIDEO_EXTENSIONS):
            video_paths.append(input_base)
    else:
        # 如果输入是目录
        for root, _, files in os.walk(input_base):
            for fname in files:
                if fname.lower().endswith(VIDEO_EXTENSIONS):
                    video_paths.append(os.path.join(root, fname))

    # 确保基础输出目录存在
    os.makedirs(output_base, exist_ok=True)

    # 已处理文件记录路径
    processed_file = os.path.join(output_base, 'processed_geometry.log')
    # 加载已处理的文件
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # 过滤掉已处理的视频
    pending_paths = [p for p in video_paths if p not in processed_set]

    # 设置独立的日志记录器
    success_log = os.path.join(output_base, 'success_geometry.log')
    error_log = os.path.join(output_base, 'error_geometry.log')

    # 错误日志记录器
    error_logger = logging.getLogger('geometry_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    # 成功日志记录器
    success_logger = logging.getLogger('geometry_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    # 使用进度条处理
    for input_path in tqdm(pending_paths, desc="Processing videos with geometry validation", unit="file"):
        # 根据相对路径和文件名确定输出目录
        fname = os.path.basename(input_path)
        base_name, _ = os.path.splitext(fname)

        if os.path.isfile(input_base):
            # 如果输入是单个文件，直接保存到输出目录
            output_dir = output_base
            output_jpg = os.path.join(output_dir, base_name+".jpg")
        else:
            # 如果输入是目录，保持目录结构
            root = os.path.dirname(input_path)
            rel_dir = os.path.relpath(root, input_base)
            output_dir = os.path.join(output_base, rel_dir)
            output_jpg = os.path.join(output_dir, base_name+".jpg")

        os.makedirs(output_dir, exist_ok=True)

        try:
            ts = find_first_valid_geometry_face(input_path, output_jpg)
            # 标记为已处理
            with open(processed_file, 'a') as pf:
                pf.write(f"{input_path}\n")
        except Exception as e:
            error_logger.error(
                f"Failed processing '{input_path}'. Error: {e}\n"
            )
        else:
            # 记录成功
            success_logger.info(f"Successfully processed: {input_path}")
            if ts != "00:00:00":
                print(f"✓ 找到有效几何形状的时间戳：{ts}")
            else:
                print(f"⚠️ 未找到有效几何形状")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Batch process videos with RetinaFace face detection and geometry validation.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
几何验证说明:
  本程序会验证人脸的几何形状，包括：
  1. 检查左眼-右眼-右嘴角-左嘴角是否构成有效四边形（无自相交）
  2. 检查鼻子是否在上述四边形内部

  只有当两个条件都满足时，才会保存最终结果图片。
  在找到有效几何形状之前，会保存中间结果（文件名带_1, _2等后缀）。

HopeNet功能说明:
  HopeNet人脸朝向检测功能通过全局变量 ENABLE_HOPENET 控制。
  要启用HopeNet功能，请在代码中将 ENABLE_HOPENET 设置为 True。

示例:
  python batch_retinaface_geometry.py input_dir output_dir
        """
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )


    args = parser.parse_args()

    print("=" * 80)
    if ENABLE_HOPENET:
        print("RetinaFace + HopeNet 人脸几何形状验证与朝向分析程序")
    else:
        print("RetinaFace 人脸几何形状验证程序")
    print("=" * 80)
    print(f"输入目录: {args.input_base}")
    print(f"输出目录: {args.output_base}")
    print(f"几何验证: {'启用' if GEOMETRY_CHECK_ENABLED else '禁用'}")
    print(f"HopeNet功能: {'启用' if ENABLE_HOPENET else '禁用'}")

    # 如果启用了HopeNet，初始化模型
    if ENABLE_HOPENET and HOPENET_AVAILABLE:
        print("\n正在初始化HopeNet模型...")
        initialize_hopenet()

    # 显示可视化说明
    print_visualization_legend()

    process_videos(args.input_base, args.output_base)

    print("=" * 80)
    print("批量处理完成！")
    print("=" * 80)

    # 再次显示可视化说明
    print_visualization_legend()


if __name__ == '__main__':
    main()