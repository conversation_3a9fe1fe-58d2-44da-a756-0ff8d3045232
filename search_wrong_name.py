import os
import argparse

def find_fix_files(root_dir, pattern='.json'):
    """
    在 root_dir 下递归查找文件名包含 pattern 的文件，并打印其路径。
    """
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            if pattern in filename:
                full_path = os.path.join(dirpath, filename)
                print(full_path)
                # os.remove(full_path)

def main():

    root_dir = "/mnt/user/media/迷奸/暗黑王子系列"
    if not os.path.isdir(root_dir):
        print(f'错误："{root_dir}" 不是一个有效目录。')
        return
    find_fix_files(root_dir)

if __name__ == '__main__':
    main()