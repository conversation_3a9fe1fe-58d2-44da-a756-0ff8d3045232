#!/usr/bin/env python3
"""
Motion Vector Analysis Script using mv-extractor

This script analyzes video motion vectors using the mv-extractor library,
calculates statistics (max, mean, median) for each frame, and outputs
annotated videos with motion statistics overlaid.

Author: Augment Agent
Date: 2024
"""

import cv2
import time
import os
import argparse
import logging
from tqdm import tqdm
import json
import numpy as np
import sys
import math
import statistics
from pathlib import Path

# mv-extractor imports
try:
    from mvextractor.videocap import VideoCap
    MV_EXTRACTOR_AVAILABLE = True
    print("✓ mv-extractor loaded successfully")
except ImportError as e:
    print(f"❌ mv-extractor not available: {e}")
    print("   Please install mv-extractor: pip install motion-vector-extractor")
    MV_EXTRACTOR_AVAILABLE = False
    sys.exit(1)

# Video file extensions
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts', '.264'
)

class MotionVectorAnalyzer:
    """Motion Vector分析器类"""
    
    def __init__(self):
        """初始化分析器"""
        self.cap = VideoCap()
        
    def calculate_motion_statistics(self, motion_vectors):
        """
        计算motion vector统计信息
        
        Args:
            motion_vectors (numpy.ndarray): motion vector数组
            
        Returns:
            dict: 包含max, mean, median的统计信息
        """
        if len(motion_vectors) == 0:
            return {
                'max_magnitude': 0.0,
                'mean_magnitude': 0.0,
                'median_magnitude': 0.0,
                'count': 0
            }
        
        # 计算每个motion vector的幅值
        magnitudes = []
        for mv in motion_vectors:
            # mv格式: [source, w, h, src_x, src_y, dst_x, dst_y, motion_x, motion_y, motion_scale]
            # 计算实际的运动向量
            dx = mv[7] / mv[9] if mv[9] != 0 else 0  # motion_x / motion_scale
            dy = mv[8] / mv[9] if mv[9] != 0 else 0  # motion_y / motion_scale
            magnitude = math.hypot(dx, dy)
            magnitudes.append(magnitude)
        
        if not magnitudes:
            return {
                'max_magnitude': 0.0,
                'mean_magnitude': 0.0,
                'median_magnitude': 0.0,
                'count': 0
            }
        
        return {
            'max_magnitude': max(magnitudes),
            'mean_magnitude': statistics.mean(magnitudes),
            'median_magnitude': statistics.median(magnitudes),
            'count': len(magnitudes)
        }
    
    def draw_motion_statistics(self, frame, stats, frame_number):
        """
        在帧上绘制motion统计信息
        
        Args:
            frame (numpy.ndarray): 视频帧
            stats (dict): 统计信息
            frame_number (int): 帧号
            
        Returns:
            numpy.ndarray: 带有统计信息的帧
        """
        # 创建副本避免修改原帧
        annotated_frame = frame.copy()
        
        # 设置文本参数
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        thickness = 2
        color = (0, 255, 255)  # 黄色
        bg_color = (0, 0, 0)   # 黑色背景
        
        # 准备文本内容
        texts = [
            f"Frame: {frame_number}",
            f"MV Count: {stats['count']}",
            f"Max: {stats['max_magnitude']:.2f}",
            f"Mean: {stats['mean_magnitude']:.2f}",
            f"Median: {stats['median_magnitude']:.2f}"
        ]
        
        # 计算文本位置
        y_offset = 30
        x_offset = 10
        line_height = 35
        
        for i, text in enumerate(texts):
            y_pos = y_offset + i * line_height
            
            # 获取文本尺寸
            (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)
            
            # 绘制背景矩形
            cv2.rectangle(annotated_frame, 
                         (x_offset - 5, y_pos - text_height - 5),
                         (x_offset + text_width + 5, y_pos + baseline + 5),
                         bg_color, -1)
            
            # 绘制文本
            cv2.putText(annotated_frame, text, (x_offset, y_pos), 
                       font, font_scale, color, thickness)
        
        return annotated_frame
    
    def process_video(self, input_path, output_path):
        """
        处理单个视频文件
        
        Args:
            input_path (str): 输入视频路径
            output_path (str): 输出视频路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 打开输入视频
            ret = self.cap.open(input_path)
            if not ret:
                raise RuntimeError(f"无法打开视频文件: {input_path}")
            
            # 获取视频信息（通过读取第一帧）
            ret, first_frame, _, _, _ = self.cap.read()
            if not ret:
                raise RuntimeError(f"无法读取视频帧: {input_path}")
            
            # 重新打开视频以从头开始
            self.cap.release()
            ret = self.cap.open(input_path)
            if not ret:
                raise RuntimeError(f"无法重新打开视频文件: {input_path}")
            
            # 获取视频属性
            height, width = first_frame.shape[:2]
            
            # 使用OpenCV获取帧率（mv-extractor可能不直接提供）
            temp_cap = cv2.VideoCapture(input_path)
            fps = temp_cap.get(cv2.CAP_PROP_FPS)
            temp_cap.release()
            
            if fps <= 0:
                fps = 25.0  # 默认帧率
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            if not out.isOpened():
                raise RuntimeError(f"无法创建输出视频: {output_path}")
            
            frame_number = 0
            
            print(f"开始处理视频: {input_path}")
            print(f"视频尺寸: {width}x{height}, 帧率: {fps}")
            
            # 处理每一帧
            while True:
                ret, frame, motion_vectors, frame_type, timestamp = self.cap.read()
                
                if not ret:
                    break
                
                # 计算motion vector统计信息
                stats = self.calculate_motion_statistics(motion_vectors)
                
                # 在帧上绘制统计信息
                annotated_frame = self.draw_motion_statistics(frame, stats, frame_number)
                
                # 写入输出视频
                out.write(annotated_frame)
                
                frame_number += 1
                
                # 每100帧显示一次进度
                if frame_number % 100 == 0:
                    print(f"已处理 {frame_number} 帧")
                if frame_number == 10000:
                    break
            
            # 清理资源
            out.release()
            self.cap.release()
            
            print(f"✓ 成功处理视频: {input_path}")
            print(f"  总帧数: {frame_number}")
            print(f"  输出文件: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理视频失败: {input_path}")
            print(f"   错误: {e}")
            
            # 清理资源
            try:
                if 'out' in locals():
                    out.release()
                self.cap.release()
            except:
                pass
            
            return False

def process_videos_batch(input_base: str, output_base: str):
    """批量处理视频文件"""

    if not MV_EXTRACTOR_AVAILABLE:
        print("❌ mv-extractor不可用，无法处理视频")
        return

    # 收集所有视频文件
    video_paths = []
    for root, _, files in os.walk(input_base):
        for fname in files:
            if fname.lower().endswith(VIDEO_EXTENSIONS):
                video_paths.append(os.path.join(root, fname))

    if not video_paths:
        print(f"❌ 在 {input_base} 中未找到视频文件")
        return

    print(f"找到 {len(video_paths)} 个视频文件")

    # 确保输出基础目录存在
    os.makedirs(output_base, exist_ok=True)

    # 处理记录文件路径
    processed_file = os.path.join(output_base, 'processed.log')
    success_log = os.path.join(output_base, 'success.log')
    error_log = os.path.join(output_base, 'error.log')

    # 加载已处理的文件
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # 过滤出待处理的视频
    pending_paths = [p for p in video_paths if p not in processed_set]

    if not pending_paths:
        print("所有视频文件都已处理完成")
        return

    print(f"待处理视频: {len(pending_paths)} 个")

    # 设置日志记录器
    error_logger = logging.getLogger('mv_analysis_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    success_logger = logging.getLogger('mv_analysis_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    # 创建分析器
    analyzer = MotionVectorAnalyzer()

    # 使用进度条处理视频
    for input_path in tqdm(pending_paths, desc="Processing videos with Motion Vector Analysis", unit="file"):
        # 确定输出路径，保持相同的文件夹结构
        root = os.path.dirname(input_path)
        fname = os.path.basename(input_path)
        rel_dir = os.path.relpath(root, input_base)
        base_name, ext = os.path.splitext(fname)

        # 输出文件名添加后缀
        output_filename = f"{base_name}_motion_analysis.mp4"
        output_dir = os.path.join(output_base, rel_dir)
        output_path = os.path.join(output_dir, output_filename)

        try:
            success = analyzer.process_video(input_path, output_path)

            if success:
                # 记录成功
                success_logger.info(f"Successfully processed: {input_path} -> {output_path}")

                # 标记为已处理
                with open(processed_file, 'a') as pf:
                    pf.write(f"{input_path}\n")
            else:
                # 记录失败
                error_logger.error(f"Failed to process: {input_path}")

        except Exception as e:
            error_logger.error(f"Exception processing '{input_path}': {e}")
            print(f"❌ 处理 {input_path} 时发生异常: {e}")

def print_usage_info():
    """打印使用说明"""
    print("\n" + "=" * 80)
    print("Motion Vector Analysis Tool - 使用说明")
    print("=" * 80)
    print("本工具使用 mv-extractor 库分析视频中的运动向量，并生成带有统计信息的视频。")
    print()
    print("功能特性:")
    print("- 提取每帧的 motion vectors")
    print("- 计算运动幅值的最大值、平均值、中位数")
    print("- 在视频左上角显示统计信息")
    print("- 保持原始文件夹结构输出")
    print("- 支持断点续传（跳过已处理文件）")
    print()
    print("输出信息说明:")
    print("- Frame: 当前帧号")
    print("- MV Count: 当前帧的运动向量数量")
    print("- Max: 运动幅值最大值")
    print("- Mean: 运动幅值平均值")
    print("- Median: 运动幅值中位数")
    print()
    print("支持的视频格式:")
    print("- H.264 编码的视频 (.mp4, .avi, .mov, .mkv 等)")
    print("- MPEG-4 Part 2 编码的视频")
    print("=" * 80)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Batch analyze motion vectors in videos using mv-extractor.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python batch_motion_vector_analysis.py input_dir output_dir
  python batch_motion_vector_analysis.py /path/to/videos /path/to/output

注意事项:
  1. 确保已安装 mv-extractor: pip install motion-vector-extractor
  2. 输入视频必须是 H.264 或 MPEG-4 Part 2 编码
  3. 输出视频将保持原始文件夹结构
  4. 处理过程中会在左上角叠加运动统计信息
        """
    )

    parser.add_argument(
        'input_base',
        help='输入目录路径，包含要分析的视频文件'
    )
    parser.add_argument(
        'output_base',
        help='输出目录路径，用于保存分析结果视频'
    )
    parser.add_argument(
        '--test-single',
        help='测试单个视频文件（用于调试）'
    )

    args = parser.parse_args()

    print("=" * 80)
    print("Motion Vector Analysis - 批量视频运动向量分析工具")
    print("=" * 80)
    print(f"输入目录: {args.input_base}")
    print(f"输出目录: {args.output_base}")

    # 检查mv-extractor可用性
    if not MV_EXTRACTOR_AVAILABLE:
        print("❌ mv-extractor 不可用，请先安装:")
        print("   pip install motion-vector-extractor")
        return

    # 显示使用说明
    print_usage_info()

    # 测试单个文件模式
    if args.test_single:
        print(f"\n🧪 测试模式: 处理单个文件 {args.test_single}")
        analyzer = MotionVectorAnalyzer()

        # 构造输出路径
        base_name = os.path.splitext(os.path.basename(args.test_single))[0]
        output_path = os.path.join(args.output_base, f"{base_name}.mp4")

        success = analyzer.process_video(args.test_single, output_path)
        if success:
            print("✓ 测试完成")
        else:
            print("❌ 测试失败")
        return

    # 批量处理模式
    print(f"\n🚀 开始批量处理...")
    process_videos_batch(args.input_base, args.output_base)

    print("\n" + "=" * 80)
    print("批量处理完成！")
    print("=" * 80)

    # 再次显示使用说明
    print_usage_info()

if __name__ == '__main__':
    main()
