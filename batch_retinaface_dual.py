import cv2
import time
import os
import argparse
import logging
from tqdm import tqdm
import json
import numpy as np
import sys
import math
from math import cos, sin

# PyTorch and related imports
import cvcuda
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
from torchvision import transforms
import torchvision
from PIL import Image

# RetinaFace相关导入
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks

# ============================================================================
# 全局配置 - 双阈值评分系统
# ============================================================================
SCORE_THRESHOLD_LOW = 0.97   # 低阈值：超过此值会保存中间结果并继续搜索
SCORE_THRESHOLD_HIGH = 0.99  # 高阈值：超过此值会保存最终结果并结束搜索
SCORE_THRESHOLD = SCORE_THRESHOLD_LOW  # 保持向后兼容性

# ============================================================================
# 全局配置 - HopeNet人脸朝向检测功能控制
# ============================================================================
ENABLE_HOPENET = False  # 默认禁用HopeNet功能，设为True启用
HOPENET_AVAILABLE = False

# 只有在启用HopeNet时才尝试加载相关依赖
if ENABLE_HOPENET:
    # Add deep-head-pose code directory to path for Hopenet
    deep_head_pose_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/code'
    if deep_head_pose_path not in sys.path:
        sys.path.append(deep_head_pose_path)

    # Import Hopenet modules
    try:
        import hopenet
        import utils
        HOPENET_AVAILABLE = True
        print("✓ Hopenet dependencies loaded successfully")
    except ImportError as e:
        print(f"⚠️ Hopenet dependencies not available: {e}")
        HOPENET_AVAILABLE = False
else:
    print("ℹ️ HopeNet人脸朝向检测功能已禁用")
    print("   如需启用，请将 ENABLE_HOPENET 设置为 True")

VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

class RetinaFaceDetector:
    """RetinaFace人脸检测器类"""

    def __init__(self):
        """初始化检测器"""
        print("正在初始化RetinaFace模型...")
        self.detector = pipeline(Tasks.face_detection, 'iic/cv_resnet50_face-detection_retinaface')
        print("✓ RetinaFace模型初始化完成")

    def detect_faces_from_array(self, image_array):
        """
        从numpy数组检测人脸

        Args:
            image_array (numpy.ndarray): 图像数组

        Returns:
            dict: 检测结果
        """
        # 将numpy数组转换为临时文件进行检测
        temp_path = "/tmp/temp_frame.jpg"
        cv2.imwrite(temp_path, image_array)

        try:
            result = self.detector(temp_path)
            return result
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    def parse_result(self, result, threshold=None):
        """
        解析检测结果

        Args:
            result (dict): 原始检测结果
            threshold (float): 可选的阈值，如果不提供则使用低阈值

        Returns:
            list: 解析后的人脸信息列表
        """
        if threshold is None:
            threshold = SCORE_THRESHOLD_LOW

        faces = []

        scores = result.get('scores', [])
        boxes = result.get('boxes', [])
        keypoints = result.get('keypoints', [])

        for i in range(len(scores)):
            if scores[i] < threshold:
                continue
            face_info = {
                'id': i + 1,
                'confidence': scores[i],
                'bbox': boxes[i],  # [x1, y1, x2, y2]
                'landmarks': keypoints[i] if i < len(keypoints) else None
            }
            faces.append(face_info)

        return faces

# Initialize Hopenet model for head pose estimation
hopenet_model = None
hopenet_transformations = None
idx_tensor = None
device = 'cuda' if torch.cuda.is_available() else 'cpu'

def initialize_hopenet():
    """Initialize Hopenet model for head pose estimation"""
    global hopenet_model, hopenet_transformations, idx_tensor

    if not ENABLE_HOPENET:
        print("ℹ️ HopeNet功能已禁用，跳过初始化")
        return False

    if not HOPENET_AVAILABLE:
        print("⚠️ Hopenet not available, skipping head pose estimation")
        return False

    try:
        print("正在初始化Hopenet模型...")

        # Initialize Hopenet model
        hopenet_model = hopenet.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)

        # Load model weights
        hopenet_weights_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/hopenet_robust_alpha1.pkl'
        if os.path.exists(hopenet_weights_path):
            print(f"加载Hopenet权重: {hopenet_weights_path}")
            saved_state_dict = torch.load(hopenet_weights_path, map_location=device)
            hopenet_model.load_state_dict(saved_state_dict)
        else:
            print("⚠️ Hopenet权重文件未找到，使用随机权重")

        # Move model to device
        if torch.cuda.is_available():
            hopenet_model.cuda()
        hopenet_model.eval()

        # Setup transformations
        hopenet_transformations = transforms.Compose([
            transforms.Resize(224),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # Setup index tensor for pose calculation
        idx_list = [idx for idx in range(66)]
        if torch.cuda.is_available():
            idx_tensor = torch.FloatTensor(idx_list).cuda()
        else:
            idx_tensor = torch.FloatTensor(idx_list)

        print("✓ Hopenet模型初始化完成")
        return True

    except Exception as e:
        print(f"❌ Hopenet模型初始化失败: {e}")
        return False

def expand_bbox_dockerface_style(x_min, y_min, x_max, y_max, img_width, img_height, expansion=50):
    """Expand bounding box using dockerface-style expansion"""
    # Apply fixed pixel expansion
    x_min -= expansion
    x_max += expansion
    y_min -= expansion
    y_max += int(expansion * 0.6)  # Less expansion on bottom

    # Ensure coordinates are within image bounds
    x_min = max(x_min, 0)
    y_min = max(y_min, 0)
    x_max = min(img_width, x_max)
    y_max = min(img_height, y_max)

    return x_min, y_min, x_max, y_max

def estimate_head_pose(image, bbox):
    """Estimate head pose for a face bounding box"""
    if not ENABLE_HOPENET:
        return None

    if hopenet_model is None or hopenet_transformations is None:
        return None

    try:
        x_min, y_min, x_max, y_max = map(int, bbox)
        img_height, img_width = image.shape[:2]

        # Expand bounding box
        expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max = expand_bbox_dockerface_style(
            x_min, y_min, x_max, y_max, img_width, img_height
        )

        # Crop face region
        face_img = image[expanded_y_min:expanded_y_max, expanded_x_min:expanded_x_max]

        if face_img.size == 0:
            return None

        # Convert to RGB for PIL
        face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
        face_pil = Image.fromarray(face_rgb)

        # Apply transformations
        transformed_img = hopenet_transformations(face_pil)
        img_shape = transformed_img.size()
        transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])

        # Move to device
        if torch.cuda.is_available():
            transformed_img = Variable(transformed_img).cuda()
        else:
            transformed_img = Variable(transformed_img)

        # Forward pass through Hopenet
        with torch.no_grad():
            yaw, pitch, roll = hopenet_model(transformed_img)

            # Apply softmax and get predictions
            yaw_predicted = F.softmax(yaw, dim=1)
            pitch_predicted = F.softmax(pitch, dim=1)
            roll_predicted = F.softmax(roll, dim=1)

            # Get continuous predictions in degrees
            yaw_predicted = torch.sum(yaw_predicted.data[0] * idx_tensor) * 3 - 99
            pitch_predicted = torch.sum(pitch_predicted.data[0] * idx_tensor) * 3 - 99
            roll_predicted = torch.sum(roll_predicted.data[0] * idx_tensor) * 3 - 99

            # Convert to float
            yaw_deg = float(yaw_predicted)
            pitch_deg = float(pitch_predicted)
            roll_deg = float(roll_predicted)

            return {
                'yaw': yaw_deg,
                'pitch': pitch_deg,
                'roll': roll_deg
            }

    except Exception as e:
        print(f"头部姿态估计错误: {e}")
        return None

def calculate_face_deviation(pose_angles):
    """Calculate face deviation from screen perpendicular"""
    if not ENABLE_HOPENET or pose_angles is None:
        return None

    yaw = pose_angles['yaw']
    pitch = pose_angles['pitch']
    roll = pose_angles['roll']

    # Calculate deviation from vertical (Z-axis perpendicular to screen)
    # For a face looking straight at camera: yaw≈0, pitch≈0, roll≈0
    deviation = np.sqrt(yaw**2 + pitch**2 + roll**2)

    # Clamp to reasonable range
    deviation = min(deviation, 90.0)

    return deviation

def draw_axis(img, yaw, pitch, roll, tdx=None, tdy=None, size=100):
    """Draw 3D coordinate axes on image"""
    if not ENABLE_HOPENET:
        return img

    pitch = pitch * np.pi / 180
    yaw = -(yaw * np.pi / 180)
    roll = roll * np.pi / 180

    if tdx is not None and tdy is not None:
        tdx = tdx
        tdy = tdy
    else:
        height, width = img.shape[:2]
        tdx = width // 2
        tdy = height // 2

    # X-Axis pointing to right, drawn in red
    x1 = size * (cos(yaw) * cos(roll)) + tdx
    y1 = size * (cos(pitch) * sin(roll) + cos(roll) * sin(pitch) * sin(yaw)) + tdy

    # Y-Axis drawn in green
    x2 = size * (-cos(yaw) * sin(roll)) + tdx
    y2 = size * (cos(pitch) * cos(roll) - sin(pitch) * sin(yaw) * sin(roll)) + tdy

    # Z-Axis (out of the screen) drawn in blue
    x3 = size * (sin(yaw)) + tdx
    y3 = size * (-cos(yaw) * sin(pitch)) + tdy

    cv2.line(img, (int(tdx), int(tdy)), (int(x1), int(y1)), (0, 0, 255), 3)  # Red X-axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x2), int(y2)), (0, 255, 0), 3)  # Green Y-axis
    cv2.line(img, (int(tdx), int(tdy)), (int(x3), int(y3)), (255, 0, 0), 2)  # Blue Z-axis

    return img

def stitch_images(image_list, grid_shape=(5, 6), save_path='stitched.jpg'):
    """
    Stitch a list of OpenCV images into a single large image and save it to disk.

    Parameters:
    - image_list: list of cv2 images (numpy arrays)
    - grid_shape: tuple (rows, cols) indicating how to arrange the images
    - save_path: file path to save the stitched image

    Returns:
    - stitched_image: the resulting large image as a numpy array
    """
    num_images = len(image_list)
    rows, cols = grid_shape

    if num_images != rows * cols:
        raise ValueError(f"Number of images ({num_images}) does not match grid shape {grid_shape}")

    # Ensure all images have the same size
    heights = [img.shape[0] for img in image_list]
    widths = [img.shape[1] for img in image_list]
    min_height, min_width = min(heights), min(widths)

    # Resize images to the smallest dimensions to keep consistency
    resized = [cv2.resize(img, (min_width, min_height)) for img in image_list]

    # Prepare a blank canvas
    stitched_height = rows * min_height
    stitched_width = cols * min_width
    # Determine number of channels (grayscale vs colour)
    channels = 1 if len(resized[0].shape) == 2 else resized[0].shape[2]
    stitched_image = np.zeros((stitched_height, stitched_width, channels), dtype=resized[0].dtype)

    # Place each image in the grid
    idx = 0
    for r in range(rows):
        for c in range(cols):
            y0 = r * min_height
            y1 = y0 + min_height
            x0 = c * min_width
            x1 = x0 + min_width
            stitched_image[y0:y1, x0:x1] = resized[idx]
            idx += 1

    # Save the result
    cv2.imwrite(save_path, stitched_image)

    return stitched_image

def check_brightness_and_contrast(img):
    # 3. 转为 HWC 布局，确保内存连续
    img_hwc: torch.Tensor = torch.from_numpy(img).contiguous().to("cuda")

    # 4. 包装为 CVCUDA Tensor，布局声明为 "HWC"
    img_tensor = cvcuda.as_tensor(img_hwc, layout="HWC")  # :contentReference[oaicite:7]{index=7}

    gray = cvcuda.cvtcolor(img_tensor, cvcuda.ColorConversion.BGR2GRAY)

    # 3. 转 PyTorch GPU Tensor 并计算平均亮度
    #    转为 [H,W] 后转 float
    gray_t = torch.as_tensor(gray.cuda()).squeeze(-1).float() * 100.0 / 255.0
    brightness = torch.mean(gray_t)  # 全局平均亮度

    # 4. 对比度图：|pixel - brightness|
    contrast_map = torch.abs(gray_t - brightness)  #
    contrast_std = torch.std(contrast_map)

    # 5. 清晰度评估：Laplacian + 方差
    lap = cvcuda.laplacian(gray, ksize=3, scale=1.0)
    lap_t = torch.as_tensor(lap.cuda()).float()
    sharpness = torch.var(lap_t)  # 方差代表高频成分强度
    return brightness, contrast_std, sharpness
# 初始化RetinaFace检测器
detector = RetinaFaceDetector()

# HopeNet模型将在需要时初始化（如果启用的话）

def find_first_face_timestamp_dual_threshold(video_path: str, save_image_path: str) -> str:
    """
    双阈值人脸检测：每隔3秒抽帧，用 RetinaFace 检测人脸。

    双阈值逻辑：
    - 若得分 > low 但 < high：保存中间结果（_1, _2, _3...），继续搜索
    - 若得分 > high：保存最终结果，结束搜索
    - 若视频末尾仍未找到 > high 的帧：返回 "00:00:00"

    :param video_path: 视频文件路径
    :param save_image_path: 检测到人脸时，保存帧图像的路径
    :return: FFmpeg 支持的时间戳字符串 "HH:MM:SS"
    """
    if not os.path.isfile(video_path):
        raise FileNotFoundError(f"视频文件不存在: {video_path}")

    # 确保保存目录存在
    save_dir = os.path.dirname(save_image_path)
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise IOError(f"无法打开视频: {video_path}")

    # 获取视频基本参数
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    sample_interval_sec = 3
    sample_step = max(1, int(fps * sample_interval_sec))

    global_idx = 0  # 全局帧计数
    intermediate_count = 0  # 中间结果计数器

    print(f"开始双阈值人脸检测 (低阈值: {SCORE_THRESHOLD_LOW}, 高阈值: {SCORE_THRESHOLD_HIGH})")

    while True:
        ret, frame = cap.read()

        if not ret:
            break  # 视频结束

        if global_idx % sample_step == 0:
            b, c, s = check_brightness_and_contrast(frame)
            if b > 10 and c > 5:
                start_time = time.time()

                h, w = frame.shape[:2]
                side = int(min(h, w))
                x_offset = (w - side) // 2
                y_offset = (h - side) // 2
                cropped_frame = frame[y_offset:y_offset + side, x_offset:x_offset + side]

                # 使用RetinaFace进行推理
                result = detector.detect_faces_from_array(cropped_frame)

                # 解析检测结果 - 使用低阈值进行初步过滤
                faces = detector.parse_result(result, threshold=SCORE_THRESHOLD_LOW)

                if faces:
                    # 过滤掉长、宽小于30像素的检测框
                    filtered_faces = []
                    for face in faces:
                        bbox = face['bbox']
                        x1, y1, x2, y2 = bbox
                        width = x2 - x1
                        height = y2 - y1
                        if width > 30 and height > 30:
                            filtered_faces.append(face)

                    # 过滤掉长、宽小于画面10%的检测框
                    final_faces = []
                    for face in filtered_faces:
                        bbox = face['bbox']
                        x1, y1, x2, y2 = bbox
                        width = x2 - x1
                        height = y2 - y1
                        if width > w*0.1 and height > h*0.1:
                            final_faces.append(face)

                    if final_faces:
                        # 获取最高置信度
                        max_confidence = max([face['confidence'] for face in final_faces])

                        # 双阈值判断逻辑
                        if max_confidence >= SCORE_THRESHOLD_HIGH:
                            # 高阈值：保存最终结果并结束
                            save_path = save_image_path
                            is_final = True
                        elif max_confidence >= SCORE_THRESHOLD_LOW:
                            # 低阈值：保存中间结果并继续
                            intermediate_count += 1
                            base_path, ext = os.path.splitext(save_image_path)
                            save_path = f"{base_path}_{intermediate_count}{ext}"
                            is_final = False
                        else:
                            # 不满足任何阈值，跳过
                            global_idx += 1
                            continue
                        # 用于存储所有人脸的偏离度，显示最小值
                        all_deviations = []
                        inference_time = (time.time() - start_time) * 1000  # 转为毫秒

                        # 绘制检测结果
                        for face in final_faces:
                            bbox = face['bbox']
                            confidence = face['confidence']
                            landmarks = face['landmarks']
                            face_id = face['id']
                            x1, y1, x2, y2 = map(int, bbox)

                            # 调整坐标到原图
                            x1, y1, x2, y2 = x1+x_offset, y1+y_offset, x2+x_offset, y2+y_offset

                            # 估计头部姿态
                            pose_angles = estimate_head_pose(frame, (x1, y1, x2, y2))

                            # 绘制边界框
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 3)

                            if pose_angles is not None:
                                # 计算人脸偏离度
                                deviation = calculate_face_deviation(pose_angles)
                                if deviation is not None:
                                    all_deviations.append(deviation)

                                # 绘制3D坐标轴
                                center_x = (x1 + x2) // 2
                                center_y = (y1 + y2) // 2
                                bbox_height = y2 - y1
                                axis_size = bbox_height // 3

                                draw_axis(frame, pose_angles['yaw'], pose_angles['pitch'], pose_angles['roll'],
                                         tdx=center_x, tdy=center_y, size=axis_size)

                                # 显示姿态信息
                                pose_text = f"Y:{pose_angles['yaw']:.1f} P:{pose_angles['pitch']:.1f} R:{pose_angles['roll']:.1f}"
                                cv2.putText(frame, pose_text, (x1, y1 - 50),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

                            # 显示置信度和ID
                            label = f"Face {face_id}: {confidence:.3f}"
                            text_y = max(y1 - 10, 20)
                            cv2.putText(
                                frame, label, (x1, text_y),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2
                            )

                            # 绘制面部关键点
                            if landmarks:
                                # 关键点颜色配置
                                landmark_colors = [
                                    (255, 0, 0),    # 左眼 - 蓝色
                                    (255, 0, 0),    # 右眼 - 蓝色
                                    (0, 255, 255),  # 鼻子 - 黄色
                                    (0, 0, 255),    # 左嘴角 - 红色
                                    (0, 0, 255)     # 右嘴角 - 红色
                                ]

                                for j in range(0, len(landmarks), 2):
                                    if j+1 < len(landmarks):
                                        # 调整关键点坐标到原图
                                        lm_x = int(landmarks[j]) + x_offset
                                        lm_y = int(landmarks[j+1]) + y_offset

                                        color_idx = j // 2
                                        color = landmark_colors[color_idx] if color_idx < len(landmark_colors) else (255, 255, 255)

                                        # 绘制关键点
                                        cv2.circle(frame, (lm_x, lm_y), 4, color, -1)
                                        cv2.circle(frame, (lm_x, lm_y), 5, (255, 255, 255), 1)  # 白色边框

                        # 显示最小偏离度（最正对屏幕的人脸）
                        if all_deviations:
                            min_deviation = min(all_deviations)
                            deviation_text = f"{min_deviation:.1f} deg"
                            # 使用大字体和醒目颜色显示偏离度
                            text_size = cv2.getTextSize(deviation_text, cv2.FONT_HERSHEY_SIMPLEX, 3, 4)[0]
                            cv2.rectangle(frame, (25, 100), (35 + text_size[0], 150), (0, 0, 0), -1)  # 黑色背景
                            cv2.putText(frame, deviation_text, (30, 140),
                                       cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 255, 255), 4)  # 黄色文字

                        # 在左上角叠加推理耗时
                        time_label = f"{inference_time:.1f} ms"
                        cv2.putText(frame, time_label, (30, 70),
                                    cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3)

                        # 显示双阈值状态信息
                        threshold_info = f"Score: {max_confidence:.3f} (L:{SCORE_THRESHOLD_LOW} H:{SCORE_THRESHOLD_HIGH})"
                        cv2.putText(frame, threshold_info, (30, 200),
                                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                        if is_final:
                            status_text = "FINAL - HIGH THRESHOLD"
                            status_color = (0, 255, 0)  # 绿色
                        else:
                            status_text = f"INTERMEDIATE #{intermediate_count} - LOW THRESHOLD"
                            status_color = (0, 255, 255)  # 黄色

                        cv2.putText(frame, status_text, (30, 240),
                                    cv2.FONT_HERSHEY_SIMPLEX, 1, status_color, 2)

                        # 保存当前帧
                        cv2.imwrite(save_path, frame)
                        timestamp = time.strftime("%H:%M:%S", time.gmtime(global_idx / fps))

                        if is_final:
                            print(f"✓ 找到高阈值人脸! 置信度: {max_confidence:.3f}, 时间戳: {timestamp}")
                            cap.release()
                            return timestamp
                        else:
                            print(f"⚠️ 低阈值人脸 #{intermediate_count}, 置信度: {max_confidence:.3f}, 继续搜索...")
                            # 继续搜索，不返回

        global_idx += 1

        if not ret or global_idx >= total_frames:
            break  # 全部读取完毕

    cap.release()
    return "00:00:00"

def print_visualization_legend():
    """打印可视化说明"""
    print("\n" + "=" * 80)
    if ENABLE_HOPENET:
        print("RetinaFace + Hopenet 双阈值人脸检测与朝向分析可视化说明:")
    else:
        print("RetinaFace 双阈值人脸检测可视化说明:")
    print("=" * 80)
    print("- 绿色矩形框: 人脸边界框")
    print("- 蓝色圆点: 眼睛位置")
    print("- 黄色圆点: 鼻子位置")
    print("- 红色圆点: 嘴角位置")
    print("- 白色边框: 关键点轮廓")
    print("- Face ID + 置信度: 人脸编号和检测置信度")
    print("- Score信息: 当前置信度和双阈值设置")
    print("- 绿色状态: FINAL - 达到高阈值，保存最终结果")
    print("- 黄色状态: INTERMEDIATE - 达到低阈值，保存中间结果并继续搜索")

    if ENABLE_HOPENET:
        print("- 红色线条: X轴 (左右方向)")
        print("- 绿色线条: Y轴 (上下方向)")
        print("- 蓝色线条: Z轴 (前后方向)")
        print("- 黄色文字: 姿态角度 (Yaw/Pitch/Roll)")
        print("- 左上角大号黄色数字: 人脸偏离度 (度数，越小越正对屏幕)")
    else:
        print("- HopeNet人脸朝向检测功能已禁用")
        print("- 如需启用朝向分析，请使用 --enable-hopenet 参数")

    print(f"- 双阈值设置: 低阈值={SCORE_THRESHOLD_LOW}, 高阈值={SCORE_THRESHOLD_HIGH}")
    print("=" * 80)

def process_videos(input_base: str, output_base: str):
    """批量处理视频文件"""
    # Collect all video files first
    video_paths = []
    for root, _, files in os.walk(input_base):
        for fname in files:
            if fname.lower().endswith(VIDEO_EXTENSIONS):
                video_paths.append(os.path.join(root, fname))

    # Ensure base output directory exists
    os.makedirs(output_base, exist_ok=True)

    # Path for record of processed files
    processed_file = os.path.join(output_base, 'processed.log')
    # Load already processed files
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())

    # Filter out already processed videos
    pending_paths = [p for p in video_paths if p not in processed_set]

    # Setup separate loggers for success and error
    success_log = os.path.join(output_base, 'success.log')
    error_log = os.path.join(output_base, 'error.log')

    # Error logger
    error_logger = logging.getLogger('retinaface_error')
    error_logger.setLevel(logging.ERROR)
    err_handler = logging.FileHandler(error_log)
    err_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    error_logger.addHandler(err_handler)

    # Success logger
    success_logger = logging.getLogger('retinaface_success')
    success_logger.setLevel(logging.INFO)
    succ_handler = logging.FileHandler(success_log)
    succ_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    success_logger.addHandler(succ_handler)

    # Process with progress bar
    for input_path in tqdm(pending_paths, desc="Processing videos with RetinaFace", unit="file"):
        # Determine output directory based on relative path and filename
        root = os.path.dirname(input_path)
        fname = os.path.basename(input_path)
        rel_dir = os.path.relpath(root, input_base)
        base_name, _ = os.path.splitext(fname)
        output_dir = os.path.join(output_base, rel_dir)
        output_jpg = os.path.join(output_dir, base_name+".jpg")
        os.makedirs(output_dir, exist_ok=True)

        try:
            ts = find_first_face_timestamp_dual_threshold(input_path, output_jpg)
            # Mark as processed
            with open(processed_file, 'a') as pf:
                pf.write(f"{input_path}\n")
        except Exception as e:
            error_logger.error(
                f"Failed processing '{input_path}'. Error: {e}\n"
            )
        else:
            # Log success
            success_logger.info(f"Successfully processed: {input_path}")
            print(f"首次检测到人脸的时间戳：{ts}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Batch process videos with RetinaFace dual-threshold face detection and optional Hopenet head pose estimation.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
双阈值配置说明:
  本程序使用双阈值评分系统：
  - 低阈值 (SCORE_THRESHOLD_LOW = 0.75): 超过此值会保存中间结果并继续搜索
  - 高阈值 (SCORE_THRESHOLD_HIGH = 0.98): 超过此值会保存最终结果并结束搜索

  输出文件命名：
  - 中间结果: <视频名>_1.jpg, <视频名>_2.jpg, <视频名>_3.jpg...
  - 最终结果: <视频名>.jpg

HopeNet配置说明:
  HopeNet人脸朝向检测功能默认禁用，可通过以下方式启用：
  1. 使用 --enable-hopenet 命令行参数
  2. 在代码中将 ENABLE_HOPENET 设置为 True

示例:
  python batch_retinaface.py input_dir output_dir
  python batch_retinaface.py input_dir output_dir --enable-hopenet
        """
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )
    parser.add_argument(
        'output_base',
        help='Path to the base output directory where results will be stored.'
    )
    parser.add_argument(
        '--enable-hopenet',
        action='store_true',
        help='Enable HopeNet head pose estimation (disabled by default)'
    )
    # 更新双阈值设置
    global SCORE_THRESHOLD_LOW, SCORE_THRESHOLD_HIGH

    parser.add_argument(
        '--low-threshold',
        type=float,
        default=SCORE_THRESHOLD_LOW,
        help=f'Low threshold for face detection (default: {SCORE_THRESHOLD_LOW})'
    )
    parser.add_argument(
        '--high-threshold',
        type=float,
        default=SCORE_THRESHOLD_HIGH,
        help=f'High threshold for face detection (default: {SCORE_THRESHOLD_HIGH})'
    )

    args = parser.parse_args()

    SCORE_THRESHOLD_LOW = args.low_threshold
    SCORE_THRESHOLD_HIGH = args.high_threshold

    # 验证阈值设置
    if SCORE_THRESHOLD_LOW >= SCORE_THRESHOLD_HIGH:
        print("❌ 错误：低阈值必须小于高阈值")
        return

    if SCORE_THRESHOLD_LOW < 0 or SCORE_THRESHOLD_HIGH > 1:
        print("❌ 错误：阈值必须在0-1之间")
        return

    # 根据命令行参数更新HopeNet设置
    global ENABLE_HOPENET, HOPENET_AVAILABLE
    if args.enable_hopenet:
        ENABLE_HOPENET = True
        print("✓ 通过命令行参数启用HopeNet功能")

        # 尝试加载HopeNet依赖
        if not HOPENET_AVAILABLE:
            deep_head_pose_path = '/home/<USER>/video_summerization/webp_generator/deep-head-pose/code'
            if deep_head_pose_path not in sys.path:
                sys.path.append(deep_head_pose_path)

            try:
                # 使用globals()来确保模块在全局作用域中可用
                globals()['hopenet'] = __import__('hopenet')
                globals()['utils'] = __import__('utils')
                HOPENET_AVAILABLE = True
                print("✓ Hopenet dependencies loaded successfully")
            except ImportError as e:
                print(f"❌ 无法加载Hopenet依赖: {e}")
                print("   HopeNet功能将被禁用")
                ENABLE_HOPENET = False

    print("=" * 80)
    if ENABLE_HOPENET:
        print("RetinaFace + Hopenet 双阈值批量视频人脸检测与朝向分析程序")
    else:
        print("RetinaFace 双阈值批量视频人脸检测程序")
    print("=" * 80)
    print(f"输入目录: {args.input_base}")
    print(f"输出目录: {args.output_base}")
    print(f"低阈值: {SCORE_THRESHOLD_LOW}")
    print(f"高阈值: {SCORE_THRESHOLD_HIGH}")
    print(f"HopeNet功能: {'启用' if ENABLE_HOPENET else '禁用'}")

    # 如果启用了HopeNet，重新初始化
    if ENABLE_HOPENET and HOPENET_AVAILABLE:
        print("\n正在初始化HopeNet模型...")
        initialize_hopenet()

    # 显示可视化说明
    print_visualization_legend()

    process_videos(args.input_base, args.output_base)

    print("=" * 80)
    print("批量处理完成！")
    print("=" * 80)

    # 再次显示可视化说明
    print_visualization_legend()

if __name__ == '__main__':
    main()
