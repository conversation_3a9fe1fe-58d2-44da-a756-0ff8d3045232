#!/usr/bin/env python3
"""
创建测试视频
"""

import cv2
import numpy as np
import os

def create_test_video():
    """创建一个简单的测试视频"""
    
    # 创建测试数据目录
    os.makedirs('test_data', exist_ok=True)
    
    # 视频参数
    width, height = 640, 480
    fps = 30
    duration = 10  # 秒
    total_frames = fps * duration
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('test_data/test_video.mp4', fourcc, fps, (width, height))
    
    print(f"创建测试视频: {total_frames} 帧, {fps} FPS, {duration} 秒")
    
    for frame_idx in range(total_frames):
        # 创建帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        frame.fill(128)  # 灰色背景
        
        # 添加一些变化的内容
        t = frame_idx / total_frames
        
        # 移动的白色矩形作为"人脸"
        face_x = int(200 + 100 * np.sin(t * 2 * np.pi))
        face_y = int(150 + 50 * np.cos(t * 2 * np.pi))
        face_w, face_h = 200, 200
        
        cv2.rectangle(frame, (face_x, face_y), (face_x + face_w, face_y + face_h), (255, 255, 255), -1)
        
        # 添加"眼睛"
        cv2.circle(frame, (face_x + 50, face_y + 50), 10, (0, 0, 0), -1)  # 左眼
        cv2.circle(frame, (face_x + 150, face_y + 50), 10, (0, 0, 0), -1)  # 右眼
        
        # 添加"鼻子"
        cv2.circle(frame, (face_x + 100, face_y + 100), 5, (0, 0, 0), -1)
        
        # 添加"嘴角"
        cv2.circle(frame, (face_x + 80, face_y + 150), 5, (0, 0, 0), -1)   # 左嘴角
        cv2.circle(frame, (face_x + 120, face_y + 150), 5, (0, 0, 0), -1)  # 右嘴角
        
        # 添加帧号
        cv2.putText(frame, f"Frame {frame_idx}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print("✓ 测试视频创建完成: test_data/test_video.mp4")

if __name__ == '__main__':
    create_test_video()
