#!/usr/bin/env python3
"""
视频处理器模块
"""

import os
import cv2
import time
from ..utils.image_utils import check_brightness_and_contrast, crop_square_center
from ..config.settings import SAMPLE_INTERVAL_SEC, MIN_BRIGHTNESS, MIN_CONTRAST


class VideoProcessor:
    """视频处理器类"""

    def __init__(self):
        """初始化视频处理器"""
        pass

    def extract_frames(self, video_path: str):
        """
        从视频中提取帧
        
        Args:
            video_path (str): 视频文件路径
            
        Yields:
            tuple: (frame, frame_index, fps, total_frames)
        """
        if not os.path.isfile(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        try:
            # 获取视频基本参数
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            sample_step = max(1, int(fps * SAMPLE_INTERVAL_SEC))

            global_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if global_idx % sample_step == 0:
                    yield frame, global_idx, fps, total_frames

                global_idx += 1

                if global_idx >= total_frames:
                    break

        finally:
            cap.release()

    def check_frame_quality(self, frame):
        """
        检查帧质量
        
        Args:
            frame: 视频帧
            
        Returns:
            bool: 帧质量是否合格
        """
        brightness, contrast, _ = check_brightness_and_contrast(frame)
        return float(brightness) > MIN_BRIGHTNESS and float(contrast) > MIN_CONTRAST

    def prepare_frame_for_detection(self, frame):
        """
        为人脸检测准备帧
        
        Args:
            frame: 原始帧
            
        Returns:
            tuple: (裁剪后的帧, x偏移, y偏移, 原始高度, 原始宽度)
        """
        h, w = frame.shape[:2]
        cropped_frame, x_offset, y_offset = crop_square_center(frame)
        return cropped_frame, x_offset, y_offset, h, w

    def calculate_timestamp(self, frame_index: int, fps: float) -> str:
        """
        计算时间戳
        
        Args:
            frame_index (int): 帧索引
            fps (float): 帧率
            
        Returns:
            str: 时间戳字符串 "HH:MM:SS"
        """
        return time.strftime("%H:%M:%S", time.gmtime(frame_index / fps))
