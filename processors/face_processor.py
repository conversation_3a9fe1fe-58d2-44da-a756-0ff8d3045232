#!/usr/bin/env python3
"""
人脸处理器模块
"""

from ..core.face_detector import create_retinaface_detector
from ..core.geometry_validator import FaceGeometryValidator
from ..core.hopenet_estimator import HopeNetEstimator
from ..config.settings import MIN_FACE_SIZE, MIN_FACE_RATIO


class FaceProcessor:
    """人脸处理器类"""

    def __init__(self, enable_hopenet=False):
        """
        初始化人脸处理器
        
        Args:
            enable_hopenet (bool): 是否启用HopeNet
        """
        self.detector = create_retinaface_detector()
        self.geometry_validator = FaceGeometryValidator()
        self.hopenet_estimator = HopeNetEstimator(enable=enable_hopenet)

    def detect_faces(self, frame):
        """
        检测人脸
        
        Args:
            frame: 输入帧
            
        Returns:
            list: 检测到的人脸列表
        """
        input_data = {'img': frame}
        result = self.detector(input_data)
        return result

    def filter_faces(self, detection_result, frame_width, frame_height):
        """
        过滤人脸检测结果
        
        Args:
            detection_result: 检测结果
            frame_width (int): 帧宽度
            frame_height (int): 帧高度
            
        Returns:
            list: 过滤后的人脸列表
        """
        filtered_faces = []

        if detection_result is not None and len(detection_result) == 2:
            dets, landms = detection_result

            if dets is not None and len(dets) > 0:
                for i in range(len(dets)):
                    det = dets[i]
                    if len(det) < 5:
                        continue

                    x1, y1, x2, y2, confidence = det[:5]

                    if confidence < 0.75:
                        continue

                    # 过滤掉长、宽小于30像素的检测框
                    width = x2 - x1
                    height = y2 - y1
                    if width <= MIN_FACE_SIZE or height <= MIN_FACE_SIZE:
                        continue

                    # 过滤掉长、宽小于画面10%的检测框
                    if width <= frame_width * MIN_FACE_RATIO or height <= frame_height * MIN_FACE_RATIO:
                        continue

                    # 获取对应的关键点
                    landmarks = None
                    if landms is not None and i < len(landms):
                        landmarks = landms[i]

                    if landmarks is not None:
                        face_info = {
                            'id': i + 1,
                            'confidence': confidence,
                            'bbox': [x1, y1, x2, y2],
                            'landmarks': landmarks
                        }
                        filtered_faces.append(face_info)

        return filtered_faces

    def find_best_geometry_face(self, faces):
        """
        找到几何形状最佳的人脸
        
        Args:
            faces (list): 人脸列表
            
        Returns:
            dict: 最佳人脸信息，包含face和geometry_score
        """
        if not faces:
            return None

        best_face = None
        best_geometry_score = -1

        for face in faces:
            landmarks = face['landmarks']
            if landmarks is not None:
                geometry_score = self.geometry_validator.calculate_geometry_score(landmarks)

                if geometry_score > best_geometry_score:
                    best_geometry_score = geometry_score
                    best_face = {
                        'face': face,
                        'geometry_score': geometry_score
                    }

        return best_face

    def validate_face_geometry(self, landmarks):
        """
        验证人脸几何形状
        
        Args:
            landmarks: 人脸关键点
            
        Returns:
            tuple: (是否为有效四边形, 鼻子是否在内部)
        """
        return self.geometry_validator.validate_face_geometry(landmarks)

    def estimate_head_pose(self, frame, bbox):
        """
        估计头部姿态
        
        Args:
            frame: 图像帧
            bbox: 人脸边界框
            
        Returns:
            dict: 姿态角度字典或None
        """
        if not self.hopenet_estimator.available:
            return None
        return self.hopenet_estimator.estimate_head_pose(frame, bbox)

    def calculate_face_deviation(self, pose_angles):
        """
        计算人脸偏离度
        
        Args:
            pose_angles (dict): 姿态角度字典
            
        Returns:
            float: 偏离角度或None
        """
        if not self.hopenet_estimator.available or pose_angles is None:
            return None
        return self.hopenet_estimator.calculate_face_deviation(pose_angles)
