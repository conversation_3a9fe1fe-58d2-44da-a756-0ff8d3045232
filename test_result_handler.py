#!/usr/bin/env python3
"""
测试结果处理器的合并方法
"""

import numpy as np
import tempfile
import os
from handlers.result_handler import ResultHand<PERSON>


def test_result_handler():
    """测试结果处理器的合并方法"""
    print("=" * 60)
    print("测试结果处理器的合并方法")
    print("=" * 60)
    
    # 创建测试用的图像帧
    test_frame = np.zeros((100, 100, 3), dtype=np.uint8)
    test_frame[:, :] = [255, 0, 0]  # 蓝色图像
    
    # 创建结果处理器
    handler = ResultHandler()
    
    # 创建临时目录进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        base_path = os.path.join(temp_dir, "test_result.jpg")
        
        print("1. 测试保存中间结果...")
        success = handler.save_result(
            test_frame, base_path, attempt_count=1, 
            is_final=False, geometry_score=1
        )
        if success:
            expected_path = os.path.join(temp_dir, "test_result_1.jpg")
            if os.path.exists(expected_path):
                print("✅ 中间结果保存成功")
            else:
                print("❌ 中间结果文件未找到")
        else:
            print("❌ 中间结果保存失败")
        
        print("\n2. 测试保存最终结果...")
        success = handler.save_result(
            test_frame, base_path, attempt_count=3, 
            is_final=True, timestamp="00:01:23"
        )
        if success:
            if os.path.exists(base_path):
                print("✅ 最终结果保存成功")
            else:
                print("❌ 最终结果文件未找到")
        else:
            print("❌ 最终结果保存失败")
        
        print("\n3. 测试向后兼容的方法...")
        # 测试旧的方法接口是否仍然工作
        success1 = handler.save_intermediate_result(test_frame, base_path, 2, 1)
        success2 = handler.save_final_result(test_frame, base_path, "00:02:34", 4)
        
        if success1 and success2:
            print("✅ 向后兼容方法正常工作")
        else:
            print("❌ 向后兼容方法失败")
        
        print("\n4. 测试其他方法...")
        # 测试几何验证
        is_valid = handler.is_geometry_valid(2)
        print(f"✅ 几何验证 (评分=2): {is_valid}")
        
        is_invalid = handler.is_geometry_valid(1)
        print(f"✅ 几何验证 (评分=1): {is_invalid}")
        
        # 测试中间结果判断
        faces = [{'confidence': 0.8}, {'confidence': 0.9}]
        should_save = handler.should_save_intermediate(faces)
        print(f"✅ 应该保存中间结果: {should_save}")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！代码合并成功")
        print("=" * 60)


if __name__ == '__main__':
    test_result_handler()
