#!/usr/bin/env python3
"""
新的简化批处理器模块
"""

from .managers.batch_manager import BatchManager


class BatchProcessor:
    """简化的批处理器类，作为BatchManager的包装器"""

    def __init__(self, enable_hopenet=False):
        """
        初始化批处理器
        
        Args:
            enable_hopenet (bool): 是否启用HopeNet
        """
        self.batch_manager = BatchManager(enable_hopenet=enable_hopenet)

    def find_first_valid_geometry_face(self, video_path: str, save_image_path: str) -> str:
        """
        在视频中找到第一个有效几何形状的人脸
        
        Args:
            video_path (str): 视频文件路径
            save_image_path (str): 保存图像路径
            
        Returns:
            str: 时间戳字符串 "HH:MM:SS"
        """
        return self.batch_manager.find_first_valid_geometry_face(video_path, save_image_path)

    def process_videos(self, input_base: str, output_base: str):
        """
        批量处理视频文件
        
        Args:
            input_base (str): 输入基础路径
            output_base (str): 输出基础路径
        """
        self.batch_manager.process_videos(input_base, output_base)
