# Face Analyzer - 人脸几何形状验证程序

## 概述

Face Analyzer 是一个基于 RetinaFace 的人脸检测和几何形状验证程序，可以批量处理视频文件，检测人脸并验证其几何形状的有效性。

## 功能特性

- **人脸检测**: 使用 RetinaFace 模型进行高精度人脸检测
- **几何验证**: 验证人脸关键点是否构成有效的几何形状
- **批量处理**: 支持批量处理视频文件
- **可视化输出**: 生成带有检测结果和验证信息的图像
- **HopeNet支持**: 可选的人脸朝向检测功能
- **进度跟踪**: 自动跟踪处理进度，支持断点续传

## 几何验证标准

程序会验证人脸的几何形状，包括：

1. **四边形检查**: 检查左眼-右眼-右嘴角-左嘴角是否构成有效四边形（无自相交）
2. **鼻子位置检查**: 检查鼻子是否在上述四边形内部

只有当两个条件都满足时（评分=2），才会保存最终结果图片。
在找到有效几何形状之前，会保存中间结果（文件名带_1, _2等后缀）。

## 安装要求

- Python 3.8+
- PyTorch
- OpenCV
- NumPy
- tqdm

## 使用方法

### 1. 直接执行

```bash
python face_analyzer/main.py input_directory output_directory
```

### 2. 模块方式执行

```bash
python -m face_analyzer.main input_directory output_directory
```

### 参数说明

- `input_directory`: 包含视频文件的输入目录（或单个视频文件路径）
- `output_directory`: 结果输出目录

## 配置选项

在 `face_analyzer/config/settings.py` 中可以配置：

- `ENABLE_HOPENET`: 启用/禁用 HopeNet 人脸朝向检测
- `SCORE_THRESHOLD`: 人脸检测置信度阈值
- `SAMPLE_INTERVAL_SEC`: 视频采样间隔（秒）
- `MIN_BRIGHTNESS`: 最小亮度阈值
- `MIN_CONTRAST`: 最小对比度阈值

## 输出文件

- `*.jpg`: 检测到有效几何形状的帧图像
- `*_1.jpg, *_2.jpg, ...`: 中间结果图像（几何验证未通过）
- `processed_geometry.log`: 已处理文件列表
- `success_geometry.log`: 成功处理日志
- `error_geometry.log`: 错误处理日志

## 可视化信息

输出图像包含以下可视化信息：

- **绿色边界框**: 检测到的人脸
- **关键点**: 眼睛、鼻子、嘴角位置
- **四边形**: 由关键点构成的几何形状
- **验证结果**: 右上角显示几何验证结果（T/F）
- **处理信息**: 推理时间、尝试次数、几何评分
- **朝向信息**: HopeNet 检测的人脸朝向（如果启用）

## 示例

```bash
# 处理单个视频文件
python face_analyzer/main.py /path/to/video.mp4 /path/to/output

# 批量处理目录中的所有视频
python face_analyzer/main.py /path/to/videos /path/to/output

# 查看帮助信息
python face_analyzer/main.py --help
```

## 注意事项

1. 程序会自动跳过已处理的文件（基于 processed_geometry.log）
2. 模型初始化需要一定时间，请耐心等待
3. 确保有足够的磁盘空间存储输出图像
4. 建议在GPU环境下运行以获得更好的性能

## 项目结构

```
face_analyzer/
├── main.py                     # 主入口程序
├── config/                     # 配置模块
│   └── settings.py
├── core/                       # 核心功能模块
│   ├── face_detector.py
│   ├── geometry_validator.py
│   └── hopenet_estimator.py
├── processors/                 # 处理器模块
│   ├── video_processor.py
│   └── face_processor.py
├── managers/                   # 管理器模块
│   └── batch_manager.py        # 批处理管理器
└── utils/                      # 工具模块（合并后）
    ├── image_utils.py          # 图像工具
    ├── visualization.py        # 可视化工具
    ├── result_handler.py       # 结果处理器
    ├── file_service.py         # 文件服务
    └── logging_service.py      # 日志服务
```
