#!/usr/bin/env python3
"""
基于RetinaFace的人脸几何形状验证脚本 - 模块化版本

新增功能：
1. 判断左眼-右眼-右嘴角-左嘴角-左眼是否构成单一封闭四边形
2. 判断鼻子是否在上述封闭四边形内部
3. 在图片右上角显示两个准则的判别结果（T/F）
4. 若准则未全部通过，继续分析直至找到第一个全部通过的帧

基于：batch_retinaface.py
作者：基于原始脚本改进
架构：模块化重构版本，使用本地RetinaFace模型
"""

import sys
import os

# 添加当前目录到Python路径，以便导入face_analyzer模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from face_analyzer.main import main

if __name__ == '__main__':
    main()
